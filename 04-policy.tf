resource "azurerm_resource_policy_assignment" "image-source" {
  count                = (var.acr_enable || var.external_acr != null) && var.enable_acr_pull_policy ? 1 : 0
  name                 = "image-source"
  resource_id          = azurerm_kubernetes_cluster.aksc.id
  policy_definition_id = "/providers/Microsoft.Authorization/policyDefinitions/febd0533-8e55-448f-b837-bd0e06f16469"
  parameters           = <<PARAMS
        {
            "effect": {
                "value": "audit"
                },
            "excludedNamespaces": {
                "value": [
                    "kube-system",
                    "gatekeeper-system"
                    ]
                },
            "allowedContainerImagesRegex": {
                "value": "${var.external_acr != null ? var.external_acr.name : (var.acr_enable ? module.acre.acre.name : "")}\\.azurecr\\.io\\/.+"
                }
        }
    PARAMS
}
