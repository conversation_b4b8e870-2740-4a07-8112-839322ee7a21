resource "azurerm_monitor_diagnostic_setting" "aksc_diag" {
  count                      = length(var.loganalytics.diag) != 0 ? length(var.loganalytics.diag) : 0
  name                       = "${local.name_aksc}-diag-${count.index}"
  target_resource_id         = azurerm_kubernetes_cluster.aksc.id
  log_analytics_workspace_id = var.loganalytics.diag[count.index].workspace_id == "" ? var.conventions.log_analytics_workspace_id : var.loganalytics.diag[count.index].workspace_id
  dynamic "enabled_log" {
    for_each = var.loganalytics.diag[count.index].log
    content {
      category = enabled_log.value
    }
  }
  dynamic "metric" {
    for_each = var.loganalytics.diag[count.index].metric
    content {
      category = metric.value
    }
  }
}

//----------------------------------------------------------------- Resource health alert
module "resource_health_aksc" {
  #checkov:skip=CKV_TF_1:Not relevant, we are using tags for releases in our environment
  count                 = var.resource_health_monitoring ? 1 : 0
  source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-alerting//activity_log_alert?ref=v1.5.0"
  resource_health_alert = true
  alert_location        = var.resource_health_alert_location
  conventions           = var.conventions
  resource_group_name   = var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name
  resource_name_suffix  = "aksc-resourcehealth-${var.resource_name_suffix}"

  scopes = [resource.azurerm_kubernetes_cluster.aksc.id]
}

// --------------------------------------------------------------- Default metric alerts
module "builtin_metrics_aksc" {
  #checkov:skip=CKV_TF_1:Not relevant, we are using tags for releases in our environment
  count               = var.builtin_metric_monitoring ? 1 : 0
  source              = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-alerting//metric_alert?ref=v1.5.0"
  conventions         = var.conventions
  resource_group_name = var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name


  metric_alerts = {
    "node_cpu_usage_percentage" = {
      resource_name_suffix = "aksc-node_cpu_usage_percentage-${var.resource_name_suffix}"
      scopes               = [resource.azurerm_kubernetes_cluster.aksc.id]
      description          = "Node CPU Usage Percentage"
      severity             = 1
      frequency            = "PT5M"
      window_size          = "PT15M"

      criteria = [
        {
          metric_namespace = "Microsoft.ContainerService/managedClusters"
          metric_name      = "node_cpu_usage_percentage"
          aggregation      = "Average"
          operator         = "GreaterThan"
          threshold        = var.alert_node_cpu_usage_percentage_threshold //default 90
        }
      ]
    },
    "node_disk_usage_percentage" = {
      resource_name_suffix = "aksc-node_disk_usage_percentage-${var.resource_name_suffix}"
      scopes               = [resource.azurerm_kubernetes_cluster.aksc.id]
      description          = "Node Disk Usage Percentage"
      severity             = 1
      frequency            = "PT5M"
      window_size          = "PT15M"

      criteria = [
        {
          metric_namespace = "Microsoft.ContainerService/managedClusters"
          metric_name      = "node_disk_usage_percentage"
          aggregation      = "Average"
          operator         = "GreaterThan"
          threshold        = var.alert_node_disk_usage_percentage_threshold //default 90
        }
      ]
    },
    "cluster_autoscaler_unschedulable_pods_count" = {
      resource_name_suffix = "aksc-cluster_autoscaler_unschedulable_pods_count-${var.resource_name_suffix}"
      scopes               = [resource.azurerm_kubernetes_cluster.aksc.id]
      description          = "Cluster Autoscaler Unschedulable Pods Count"
      severity             = 1
      frequency            = "PT15M"
      window_size          = "PT30M"

      criteria = [
        {
          metric_namespace = "Microsoft.ContainerService/managedClusters"
          metric_name      = "cluster_autoscaler_unschedulable_pods_count"
          aggregation      = "Average"
          operator         = "GreaterThan"
          threshold        = var.alert_cluster_autoscaler_unschedulable_pods_count_threshold
        }
      ]
    },
    "kube_pod_status_phase" = {
      resource_name_suffix = "aksc-kube_pod_status_phase-${var.resource_name_suffix}"
      scopes               = [resource.azurerm_kubernetes_cluster.aksc.id]
      description          = "Pods Failed/Unknown state"
      severity             = 1
      frequency            = "PT1M"
      window_size          = "PT1M"

      criteria = [
        {
          metric_namespace = "Microsoft.ContainerService/managedClusters"
          metric_name      = "kube_pod_status_phase"
          aggregation      = "Total"
          operator         = "GreaterThan"
          threshold        = var.alert_kube_pod_status_phase_threshold
          dimension = [{
            name   = "phase"
            values = ["Failed", "Unknown"]
          }]
        }
      ]
    },

  }
}  