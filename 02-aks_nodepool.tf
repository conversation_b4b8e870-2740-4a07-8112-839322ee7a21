resource "azurerm_kubernetes_cluster_node_pool" "aks_nodepool" {
  #checkov:skip=CKV_AZURE_227:Missing AzureRM v4 support - fixed in later checkov release
  #checkov:skip=CKV_AZURE_168:False positive - the requirement is meet
  for_each = var.aks_nodepool != null ? { for obj in var.aks_nodepool : obj.name => obj } : {}

  #Timeouts
  timeouts {
    create = coalesce(each.value.timeout_create, var.aks_nodepool_timeout_create)
    update = coalesce(each.value.timeout_update, var.aks_nodepool_timeout_update)
    read   = coalesce(each.value.timeout_read, var.aks_nodepool_timeout_read)
    delete = coalesce(each.value.timeout_delete, var.aks_nodepool_timeout_delete)
  }

  #Tags
  tags = each.value.custom_tags != null ? merge(local.tags, each.value.custom_tags) : local.tags

  #General
  name                  = each.value.name
  temporary_name_for_rotation = each.value.temporary_name_for_rotation
  kubernetes_cluster_id = resource.azurerm_kubernetes_cluster.aksc.id
  mode                  = each.value.mode
  orchestrator_version  = each.value.kubernetes_version
  vnet_subnet_id        = coalesce(each.value.nodepool_subnet_id,var.subnet_object.id)

  #Optional
  host_encryption_enabled  = true
  node_public_ip_enabled   = each.value.node_public_ip_enabled
  node_public_ip_prefix_id = each.value.node_public_ip_prefix_id
  fips_enabled             = each.value.fips_enabled
  workload_runtime         = each.value.workload_runtime

  #Autoscaling
  auto_scaling_enabled = each.value.auto_scaling_enabled
  min_count            = each.value.auto_scaling_enabled ? each.value.min_count : null
  max_count            = each.value.auto_scaling_enabled ? each.value.max_count : null
  node_count           = each.value.auto_scaling_enabled ? each.value.min_count : each.value.node_count

  #Upgrade
  dynamic "upgrade_settings" {
    for_each = each.value.max_surge != null ? ["upgrade_settings"] : []
    content {
      max_surge = coalesce(each.value.max_surge, "1")
      drain_timeout_in_minutes = each.value.drain_timeout_in_minutes
      node_soak_duration_in_minutes = each.value.node_soak_duration_in_minutes
    }
  }

  #Node Config
  vm_size                      = each.value.vm_size
  zones                        = each.value.zones
  os_type                      = each.value.os_type
  os_sku                       = each.value.os_type != "Windows" ? coalesce(each.value.os_sku, "Ubuntu") : null
  ultra_ssd_enabled            = each.value.ultra_ssd_enabled
  os_disk_type                 = each.value.os_disk_type
  os_disk_size_gb              = each.value.os_disk_size_gb
  proximity_placement_group_id = each.value.proximity_placement_group_id
  scale_down_mode              = each.value.scale_down_mode

  priority        = each.value.priority
  eviction_policy = each.value.eviction_policy
  spot_max_price  = each.value.spot_max_price

  pod_subnet_id = each.value.pod_subnet_id
  max_pods      = each.value.max_pods
  node_labels   = each.value.node_labels
  node_taints   = each.value.node_taints

  #Kubelet Config
  kubelet_disk_type = each.value.kubelet_disk_type
  kubelet_config {
    allowed_unsafe_sysctls    = each.value.kubelet_config_allowed_unsafe_sysctls
    container_log_max_line    = each.value.kubelet_config_container_log_max_line
    container_log_max_size_mb = each.value.kubelet_config_container_log_max_size_mb
    cpu_manager_policy        = each.value.kubelet_config_cpu_manager_policy
    cpu_cfs_quota_enabled     = each.value.kubelet_config_cpu_cfs_quota_enabled
    cpu_cfs_quota_period      = each.value.kubelet_config_cpu_cfs_quota_period
    image_gc_high_threshold   = each.value.kubelet_config_image_gc_high_threshold
    image_gc_low_threshold    = each.value.kubelet_config_image_gc_low_threshold
    pod_max_pid               = each.value.kubelet_config_pod_max_pid
    topology_manager_policy   = each.value.kubelet_config_topology_manager_policy
  }

  #linux config
  linux_os_config {
    sysctl_config {
      # file handle limits
      fs_file_max                 = each.value.custom_node_config_file_handle_max
      fs_nr_open                  = each.value.custom_node_config_file_number_open
      fs_inotify_max_user_watches = each.value.custom_node_config_file_inotify_max
      fs_aio_max_nr               = each.value.custom_node_config_file_aio_max

      # worker limits
      kernel_threads_max = each.value.custom_node_config_kernel_threads_max

      # socket and network tuning
      ## net_core
      net_core_somaxconn          = each.value.custom_node_config_network_connection_max
      net_core_netdev_max_backlog = each.value.custom_node_config_network_dev_backlog_max
      net_core_rmem_default       = each.value.custom_node_config_network_socket_receive_buffer_default
      net_core_rmem_max           = each.value.custom_node_config_network_socket_receive_buffer_max
      net_core_wmem_default       = each.value.custom_node_config_network_socket_send_buffer_default
      net_core_wmem_max           = each.value.custom_node_config_network_socket_send_buffer_max
      net_core_optmem_max         = each.value.custom_node_config_network_socket_option_memory_max

      ## net_ipv4
      net_ipv4_tcp_max_syn_backlog      = each.value.custom_node_config_network_ipv4_connection_request_backlog_max
      net_ipv4_tcp_max_tw_buckets       = each.value.custom_node_config_network_ipv4_timewait_bucket
      net_ipv4_tcp_tw_reuse             = each.value.custom_node_config_network_ipv4_timewait_reuse
      net_ipv4_tcp_fin_timeout          = each.value.custom_node_config_network_ipv4_fin_timeout
      net_ipv4_tcp_keepalive_time       = each.value.custom_node_config_network_ipv4_tcp_keepalive_timeout
      net_ipv4_tcp_keepalive_probes     = each.value.custom_node_config_network_ipv4_tcp_keepalive_probes
      net_ipv4_tcp_keepalive_intvl      = each.value.custom_node_config_network_ipv4_tcp_probe_interval
      net_ipv4_ip_local_port_range_min  = each.value.custom_node_config_network_ipv4_local_port_range_min
      net_ipv4_ip_local_port_range_max  = each.value.custom_node_config_network_ipv4_local_port_range_max
      net_ipv4_neigh_default_gc_thresh1 = each.value.custom_node_config_network_ipv4_arp_cache_gc_min
      net_ipv4_neigh_default_gc_thresh2 = each.value.custom_node_config_network_ipv4_arp_cache_gc_soft_max
      net_ipv4_neigh_default_gc_thresh3 = each.value.custom_node_config_network_ipv4_arp_cache_gc_max

      ## net_netfilter
      net_netfilter_nf_conntrack_max     = each.value.custom_node_config_network_ipv4_nat_connection_max
      net_netfilter_nf_conntrack_buckets = each.value.custom_node_config_network_ipv4_nat_bucket_max

      ## virtual memory
      vm_max_map_count      = each.value.custom_node_config_vm_max_map_count
      vm_swappiness         = each.value.custom_node_config_vm_swappiness
      vm_vfs_cache_pressure = each.value.custom_node_config_vm_vfs_cache_pressure

    }

    swap_file_size_mb             = each.value.custom_node_config_swap_file_size_mb
    transparent_huge_page_enabled = each.value.custom_node_config_transparent_huge_page_enabled
    transparent_huge_page_defrag  = each.value.custom_node_config_transparent_huge_page_defrag
  }

  depends_on = [ 
    azurerm_role_assignment.aks-subnet-role-assignment
  ]

}