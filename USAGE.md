<!-- BEGIN_TF_DOCS -->
## Name of the module
Azure Kubernetes Services

shortname: aks

terraform resource: azurerm\_kubernetes\_cluster

## Short description of the module
This Terraform module deploys Azure Kubernetes Cluster.

## Detailed description on Confluence
[Azure Kubernetes Service](https://confluence.otpbank.hu/x/TWAoKQ)

## Terraform version compatibility
Terraform >= v1.3.6

## Necessary Terraform providers, and compatibility to provider versions
- provider registry.terraform.io/hashicorp/azurerm >= 4.1.0
- provider registry.terraform.io/hashicorp/time >= 0.9.1
- provider registry.terraform.io/hashicorp/tfcoremock >= 0.1.2
- provider registry.terraform.io/hashicorp/null >= 3.2.1

## Release notes – changes in the current and previous versions
[CHANGELOG.md](CHANGELOG.md)

## Resources generated by the module
- Azure Kubernetes Cluster
- Azure Container Registry
- Resource Group
- Resource health and metric alerts

## Requirements

The following requirements are needed by this module:

- <a name="requirement_azurerm"></a> [azurerm](#requirement\_azurerm) (>= 4.16.0)

- <a name="requirement_null"></a> [null](#requirement\_null) (>= 3.2.1)

- <a name="requirement_tfcoremock"></a> [tfcoremock](#requirement\_tfcoremock) (>= 0.1.2)

- <a name="requirement_time"></a> [time](#requirement\_time) (>= 0.9.1)

## Providers

The following providers are used by this module:

- <a name="provider_azurerm"></a> [azurerm](#provider\_azurerm) (>= 4.16.0)

- <a name="provider_tfcoremock"></a> [tfcoremock](#provider\_tfcoremock) (>= 0.1.2)

- <a name="provider_time"></a> [time](#provider\_time) (>= 0.9.1)


## Example for Provider configuration

```hcl
# Configure the Microsoft Azure Provider
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
}

provider "tfcoremock" {
  use_only_state = true
}

provider "azurerm" {
  features {}
  alias = "logging"
  subscription_id = "a3a538d4-3d2b-4b05-b268-f147b8788b86"
}

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.16.0" //testing with minimum provider version
    }
    time = {
      source  = "hashicorp/time"
      version = "0.9.1"
    }
  }
}

```

## Example for Convention

```hcl
module "conventions" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.1.0"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality" = "low"
  }
}

```

## Example for AKS creation

```hcl
locals {
  resource_name = "akstest0515"
}

module "k8s" {
  source                = "../.."
  //source             = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks?ref=v2.3.1"
  conventions           = module.conventions
  resource_name_suffix  = local.resource_name
  subnet_object         = data.azurerm_subnet.snet-aks
  use_proxy             = true
  linux_admin_usr       = "nodeadmin"
  linux_admin_key       = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC2HrLWQnkgaI3oyP/hBBKFpTFGgSvgdklW8/fB56oJVK2tUYdOJGpqKDFWzJMChmDYRSt9o7cJ3Gisfv+Urud2UjS+Gh0X7Vj1fycYnYnn6POr4mAnuIemhD/wrzJJzWj09vbdMRBuZRMGyQsxcVAExwnX0NtnY0F1LV7aIloWrZqSu7S/ft86HuTbrb3eMOctI1gV/noOlrVXUXnbMozEpKQtKese2vJgenOVZmxlVJpJkRZ1tyClETzLsSNU/5qNlkqGy9g+f2cNXjcVZUIlHpjR+SOtlnkmtl5nhCTaaofAmQtPwdh/UsKFAGfOqFzS+5J+NMabTpwjPDVsFOC9 rsa-key-20221202"
  #ingress_type          = "nginx"
  #ingress_nginx_ip      = var.ingress_nginx_ip
  prometheus_enable     = true
  temporary_name_for_rotation = "temptestname"
  only_critical_addons_enabled  = true

  sys_pool_node_size      = "Standard_D4ds_v5"
  sys_pool_node_count_max = 3
  sys_pool_node_labels = {
    name = "testlabel01"
  }

  // Below parameter needs to be used only if you need to deploy multiple AKS in the same environment with same project name
  acr_suffix = "0321"
/*
  // Default logging is disable from v1.4.4. You need to configure logging explicitely like below. Default workspace is the shared log analytics workspace.
  loganalytics = {
    defender = ""
    oms      = ""
    diag = [{
      log = ["kube-apiserver",
      "kube-audit",
      "kube-controller-manager",
      "kube-scheduler",
      "cluster-autoscaler",
      "kube-audit-admin",
      "guard",
      "cloud-controller-manager",
      "csi-azuredisk-controller",
      "csi-azurefile-controller",
      "csi-snapshot-controller"]
      metric = []
      workspace_id = module.conventions.log_analytics_workspace_id
    }]

  }
*/ 

  // Use below block if logging is not required
  loganalytics = {
    defender = null
    oms      = module.conventions.log_analytics_workspace_id
    diag = []
  }

  # Configure container insights
  container_insights_enable = true
  container_insights_configmap_filename = "ama-logs-rs-config_v2.yaml"

  // Builtin monitoring can be disabled here
  builtin_metric_monitoring   = false
  resource_health_monitoring  = false

  // Enable workload identity
  // aks_workload_identity_enabled = true

  // Create a dedicated user node pool with minimal set of parameters - see example 13 for complete example
  aks_nodepool = [
    {
      name                 = "testpool1"
      nodepool_subnet_id   = data.azurerm_subnet.snet-aks.id
      auto_scaling_enabled = false
      node_count           = 1 // needed when enable_auto_scaling = false
      vm_size  = "Standard_D4ds_v5"
      max_pods = 50
    }
  ]

}



```


## Resources

The following resources are used by this module:

- [azurerm_kubernetes_cluster.aksc](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/kubernetes_cluster) (resource)
- [azurerm_kubernetes_cluster_node_pool.aks_nodepool](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/kubernetes_cluster_node_pool) (resource)
- [azurerm_monitor_data_collection_rule.dcr](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/monitor_data_collection_rule) (resource)
- [azurerm_monitor_data_collection_rule_association.dcra](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/monitor_data_collection_rule_association) (resource)
- [azurerm_monitor_data_collection_rule_association.dcra-endpoint](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/monitor_data_collection_rule_association) (resource)
- [azurerm_monitor_diagnostic_setting.aksc_diag](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/monitor_diagnostic_setting) (resource)
- [azurerm_resource_policy_assignment.image-source](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/resource_policy_assignment) (resource)
- [azurerm_role_assignment.acr-kubelet-role-assignment](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment) (resource)
- [azurerm_role_assignment.aks-current-rbac-admin-role-assignment](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment) (resource)
- [azurerm_role_assignment.aks-route-table-role-assignment](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment) (resource)
- [azurerm_role_assignment.aks-subnet-role-assignment](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment) (resource)
- [azurerm_role_assignment.aks_cluster_role_des_cluster_reader](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment) (resource)
- [azurerm_role_assignment.aks_cluster_role_des_reader](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment) (resource)
- [azurerm_role_assignment.kubelet-role-assignment](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment) (resource)
- [azurerm_security_center_assessment.aksc_asc_assessment](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/security_center_assessment) (resource)
- [azurerm_security_center_assessment_policy.aksc_asc_policy](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/security_center_assessment_policy) (resource)
- [azurerm_user_assigned_identity.umid-aks](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/user_assigned_identity) (resource)
- [azurerm_user_assigned_identity.umid-kubelet](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/user_assigned_identity) (resource)
- [tfcoremock_complex_resource.ama-config](https://registry.terraform.io/providers/hashicorp/tfcoremock/latest/docs/resources/complex_resource) (resource)
- [tfcoremock_complex_resource.helm-prometheus](https://registry.terraform.io/providers/hashicorp/tfcoremock/latest/docs/resources/complex_resource) (resource)
- [tfcoremock_complex_resource.ingress-nginx](https://registry.terraform.io/providers/hashicorp/tfcoremock/latest/docs/resources/complex_resource) (resource)
- [tfcoremock_complex_resource.sysctl_config](https://registry.terraform.io/providers/hashicorp/tfcoremock/latest/docs/resources/complex_resource) (resource)
- [time_sleep.wait-private-dns](https://registry.terraform.io/providers/hashicorp/time/latest/docs/resources/sleep) (resource)

## Required Inputs

The following input variables are required:

### <a name="input_conventions"></a> [conventions](#input\_conventions)

Description: (Required) terraform-conventions module

Type: `any`

### <a name="input_resource_name_suffix"></a> [resource\_name\_suffix](#input\_resource\_name\_suffix)

Description: (Required) Custom resource name suffix

Type: `string`

### <a name="input_subnet_object"></a> [subnet\_object](#input\_subnet\_object)

Description: (Required) Subnet object where the resources will be installed to

Type: `any`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_acr_admin_token_user"></a> [acr\_admin\_token\_user](#input\_acr\_admin\_token\_user)

Description: (Optional) Admin token name - used as username while accessing ACR

Type: `string`

Default: `"admintoken"`

### <a name="input_acr_enable"></a> [acr\_enable](#input\_acr\_enable)

Description: (Optional) Enable Azure Container Registry. If enabled, container registry automatically created.

Type: `bool`

Default: `true`

### <a name="input_acr_generate_admin_token"></a> [acr\_generate\_admin\_token](#input\_acr\_generate\_admin\_token)

Description: (Optional) Generate and export admin access token to key vault

Type: `bool`

Default: `false`

### <a name="input_acr_log_analytics_diag_logs"></a> [acr\_log\_analytics\_diag\_logs](#input\_acr\_log\_analytics\_diag\_logs)

Description: (Optional)List log types need to be sent to Log Analytics Workspace from ACR. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories

Type: `list(string)`

Default: `[]`

### <a name="input_acr_log_analytics_metrics"></a> [acr\_log\_analytics\_metrics](#input\_acr\_log\_analytics\_metrics)

Description: (Optional)List metrics need to be sent to Log Analytics Workspace from ACR. Set AllMetrics to send all available metric types.

Type: `list(string)`

Default: `[]`

### <a name="input_acr_log_analytics_workspace_id"></a> [acr\_log\_analytics\_workspace\_id](#input\_acr\_log\_analytics\_workspace\_id)

Description: (Optional) ID of target Log Analytics Workspace for ACR logging.

Type: `string`

Default: `null`

### <a name="input_acr_suffix"></a> [acr\_suffix](#input\_acr\_suffix)

Description: (Optional) ACR suffix need to be defined in case of resource name conflict

Type: `string`

Default: `""`

### <a name="input_admin_group_object_ids"></a> [admin\_group\_object\_ids](#input\_admin\_group\_object\_ids)

Description: (Optional) A list of Object IDs of Azure Active Directory Groups which should have Admin Role on the Cluster.

Type: `list(string)`

Default: `null`

### <a name="input_agic_subnet_id"></a> [agic\_subnet\_id](#input\_agic\_subnet\_id)

Description: (Conditionally required) Subnet ID for Application Gateway Ingress Controller. Required if ingress\_type = agic

Type: `string`

Default: `""`

### <a name="input_aks_automatic_upgrade_channel"></a> [aks\_automatic\_upgrade\_channel](#input\_aks\_automatic\_upgrade\_channel)

Description: (Optional) The upgrade channel for this Kubernetes Cluster. Possible values are patch, rapid, node-image and stable. Omitting this field sets this value to none.

Type: `string`

Default: `"none"`

### <a name="input_aks_cmk_enabled"></a> [aks\_cmk\_enabled](#input\_aks\_cmk\_enabled)

Description: (Optional) Set this variable to true if you would like to create disk encryption set and AKS cluster in one deployment step (see example 14). It defaults to false. If aks\_disk\_encryption\_id already exists this variable can be omitted.

Type: `bool`

Default: `false`

### <a name="input_aks_disk_encryption_set_id"></a> [aks\_disk\_encryption\_set\_id](#input\_aks\_disk\_encryption\_set\_id)

Description: (Optional) ID of the disk encryption set used to encrypt node disks and persistent volumes. For more details please visit https://docs.microsoft.com/en-us/azure/aks/azure-disk-customer-managed-keys.

Type: `string`

Default: `null`

### <a name="input_aks_maintenance_window_auto_upgrade"></a> [aks\_maintenance\_window\_auto\_upgrade](#input\_aks\_maintenance\_window\_auto\_upgrade)

Description: (Optional) Maintenance window for auto upgrade

Type:

```hcl
object({
    frequency = optional(string)
    interval = optional(number)
    duration = optional(number)
    day_of_week = optional(string)
    day_of_month = optional(string)
    week_index = optional(string)
    start_time = optional(string)
    utc_offset = optional(string)
    start_date = optional(string)
    not_allowed = optional(list(object({
      start = string
      end = string
    })), [])
  })
```

Default: `null`

### <a name="input_aks_maintenance_window_node_os"></a> [aks\_maintenance\_window\_node\_os](#input\_aks\_maintenance\_window\_node\_os)

Description: (Optional) Maintenance window for node OS upgrade

Type:

```hcl
object({
    frequency = optional(string)
    interval = optional(number)
    duration = optional(number)
    day_of_week = optional(string)
    day_of_month = optional(string)
    week_index = optional(string)
    start_time = optional(string)
    utc_offset = optional(string)
    start_date = optional(string)
    not_allowed = optional(list(object({
      start = string
      end = string
    })), [])
  })
```

Default: `null`

### <a name="input_aks_network_data_plane"></a> [aks\_network\_data\_plane](#input\_aks\_network\_data\_plane)

Description: (Optional) Specifies the data plane used for building the Kubernetes network. Possible values are azure and cilium. Defaults to azure. Disabling this forces a new resource to be created.

Type: `string`

Default: `null`

### <a name="input_aks_network_dns_service_ip"></a> [aks\_network\_dns\_service\_ip](#input\_aks\_network\_dns\_service\_ip)

Description: (Optional) IP address within the Kubernetes service address range that will be used by cluster service discovery (kube-dns). Changing this forces a new resource to be created.

Type: `string`

Default: `null`

### <a name="input_aks_network_plugin"></a> [aks\_network\_plugin](#input\_aks\_network\_plugin)

Description: (Optional) Network plugin to use for networking. Currently supported values are azure, kubenet and none. Changing this forces a new resource to be created.

Type: `string`

Default: `"azure"`

### <a name="input_aks_network_plugin_mode"></a> [aks\_network\_plugin\_mode](#input\_aks\_network\_plugin\_mode)

Description: (Optional) Specifies the network plugin mode used for building the Kubernetes network. Possible value is overlay.

Type: `string`

Default: `"overlay"`

### <a name="input_aks_network_policy"></a> [aks\_network\_policy](#input\_aks\_network\_policy)

Description: (Optional) Sets up network policy to be used. Network policy allows us to control the traffic flow between pods. Currently supported values are calico, azure and cilium. Changing this forces a new resource to be created. For more details please visit https://docs.microsoft.com/en-us/azure/aks/use-network-policies.

Type: `string`

Default: `"azure"`

### <a name="input_aks_node_os_upgrade_channel"></a> [aks\_node\_os\_upgrade\_channel](#input\_aks\_node\_os\_upgrade\_channel)

Description: (Optional) The upgrade channel for this Kubernetes Cluster Nodes' OS Image. Possible values are Unmanaged, SecurityPatch, NodeImage and None. Defaults to NodeImage.

Type: `string`

Default: `"None"`

### <a name="input_aks_nodepool"></a> [aks\_nodepool](#input\_aks\_nodepool)

Description:   (Optional) The AKS Node Pools to be created

  GENERAL

  `name`  - (Required) The name of the Node Pool which should be created within the Kubernetes Cluster.  
            A Windows Node Pool cannot have a name longer than 6 characters.  
            Name needs to be unique within a deployment.  
            Changing this forces a new resource to be created.

  `temporary_name_for_rotation` - (Optional) Specifies the name of the temporary node pool used to cycle the default node pool for VM resizing.

  `mode`  - (Optional) Should this Node Pool be used for System or User resources?  
            Possible values are System and User.  
            Defaults to User.

  `kubernetes_version`  - (Optional) Version of Kubernetes used for the Agents.  
                          If not specified, the latest recommended version will be used at provisioning time (but won't auto-upgrade).  
                          AKS does not require an exact patch version to be specified, minor version aliases such as 1.22 are also supported.  
                          The minor version's latest GA patch is automatically chosen in that case.  
                          This version must be supported by the Kubernetes Cluster - as such the version of Kubernetes used on the Cluster/Control Plane may need to be upgraded first.

  `nodepool_subnet_id`  - (Optional) ID of the subnet where this Node Pool should exist. Changing this forces a new resource to be created.  
                      At this time the Virtual Network must be the same for all node pools in the cluster.  
                      A route table must be configured on this Subnet.  
                      If not defined it defaults to the subnet\_object used for system nodepool.

  OPTIONAL

  `host_encryption_enabled`   - (Optional) Should the nodes in this Node Pool have host encryption enabled? Defaults to true.

  `node_public_ip_enabled`    - (Optional) Should each node have a Public IP Address? Defaults to false. Changing this forces a new resource to be created.

  `node_public_ip_prefix_id`  - (Optional) Resource ID for the Public IP Addresses Prefix for the nodes in this Node Pool. `node_public_ip_enabled` should be true. Changing this forces a new resource to be created.

  `fips_enabled`              - (Optional) Should the nodes in this Node Pool have Federal Information Processing Standard enabled? Changing this forces a new resource to be created.  
                                FIPS support is in Public Preview.  
                                Information and details on how to opt into the Preview can be found at https://learn.microsoft.com/en-us/azure/aks/use-multiple-node-pools#add-a-fips-enabled-node-pool-preview.

  `workload_runtime`         - (Optional) Used to specify the workload runtime. Allowed values are OCIContainer, WasmWasi and KataMshvVmIsolation.

                                WebAssembly System Interface node pools are in Public Preview.  
                                Information and details on how to opt into the preview can be found at https://learn.microsoft.com/en-us/azure/aks/use-wasi-node-pools.

                                Pod Sandboxing / KataVM Isolation node pools are in Public Preview.  
                                Information and details on how to opt into the preview can be found at https://learn.microsoft.com/en-us/azure/aks/use-pod-sandboxing.

  KUBELET

  `kubelet_disk_type`        - (Optional) The type of disk used by kubelet. Possible values are OS and Temporary. Defaults to OS.

  `kubelet_config`           - (Optional) A kubelet\_config block as defined below.

    `kubelet_config_allowed_unsafe_sysctls`    - (Optional) Specifies the allow list of unsafe sysctls command or patterns (ending in *). Changing this forces a new resource to be created.

    `kubelet_config_container_log_max_line`    - (Optional) Specifies the maximum number of container log files that can be present for a container. must be at least 2. Changing this forces a new resource to be created.

    `kubelet_config_container_log_max_size_mb` - (Optional) Specifies the maximum size (e.g. 10MB) of container log file before it is rotated. Changing this forces a new resource to be created.

    `kubelet_config_cpu_manager_policy`        - (Optional) Specifies the CPU Manager policy to use. Possible values are none and static, Changing this forces a new resource to be created.

    `kubelet_config_cpu_cfs_quota_enabled`     - (Optional) Is CPU CFS quota enforcement for containers enabled? Changing this forces a new resource to be created.

    `kubelet_config_cpu_cfs_quota_period`      - (Optional) Specifies the CPU CFS quota period value. Changing this forces a new resource to be created.

    `kubelet_config_image_gc_high_threshold`   - (Optional) Specifies the percent of disk usage above which image garbage collection is always run. Must be between 0 and 100. Changing this forces a new resource to be created.

    `kubelet_config_image_gc_low_threshold`    - (Optional) Specifies the percent of disk usage lower than which image garbage collection is never run. Must be between 0 and 100. Changing this forces a new resource to be created.

    `kubelet_config_pod_max_pid`               - (Optional) Specifies the maximum number of processes per pod. Changing this forces a new resource to be created.

    `kubelet_config_topology_manager_policy`    - (Optional) Specifies the Topology Manager policy to use. Possible values are none, best-effort, restricted or single-numa-node. Changing this forces a new resource to be created.

  AUTOSCALING

  `auto_scaling_enabled` - (Optional) Whether to enable auto-scaler. Defaults to false.

  `min_count`            - (Optional) The minimum number of nodes which should exist within this Node Pool. Valid values are between 0 and 1000 and must be less than or equal to max\_count.

  `max_count`            - (Optional) The maximum number of nodes which should exist within this Node Pool. Valid values are between 0 and 1000 and must be greater than or equal to min\_count.

  `node_count`           - (Optional) The initial number of nodes which should exist within this Node Pool. Valid values are between 0 and 1000 (inclusive) for user pools and between 1 and 1000 (inclusive) for system pools and must be a value in the range min\_count - max\_count.

  UPGRADE

  `max_surge` - (Optional) The maximum number or percentage of nodes which will be added to the Node Pool size during an upgrade.  
                If a percentage is provided, the number of surge nodes is calculated from the current node count on the cluster.  
                Node surge can allow a cluster to have more nodes than max\_count during an upgrade.  
                Ensure that your cluster has enough IP space during an upgrade.

  NODES

  `vm_size`                      - (Required) The SKU which should be used for the Virtual Machines used in this Node Pool. Changing this forces a new resource to be created.

  `zones`                        - (Optional) Specifies a list of Availability Zones in which this Kubernetes Cluster Node Pool should be located. Changing this forces a new Kubernetes Cluster Node Pool to be created.

  `os_type`                      - (Optional) The Operating System which should be used for this Node Pool. Changing this forces a new resource to be created. Possible values are Linux and Windows. Defaults to Linux.

  `os_sku`                       - (Optional) OsSKU to be used to specify Linux OSType. Not applicable to Windows OSType. Possible values include: Ubuntu, CBLMariner. Defaults to Ubuntu. Changing this forces a new resource to be created.

  `ultra_ssd_enabled`            - (Optional) Used to specify whether the UltraSSD is enabled in the Node Pool. Defaults to false.  
                                    Check https://learn.microsoft.com/en-us/azure/aks/use-ultra-disks for more information.

  `os_disk_type`                 - (Optional) The type of disk which should be used for the Operating System. Possible values are Ephemeral and Managed. Defaults to Managed. Changing this forces a new resource to be created.

  `os_disk_size_gb`              - (Optional) The Agent Operating System disk size in GB. Changing this forces a new resource to be created.

  `proximity_placement_group_id` - (Optional) The ID of the Proximity Placement Group where the Virtual Machine Scale Set that powers this Node Pool will be placed. Changing this forces a new resource to be created.

  `scale_down_mode`              - (Optional) Specifies how the node pool should deal with scaled-down nodes. Allowed values are Delete and Deallocate. Defaults to Delete.

  `priority`                     - (Optional) The Priority for Virtual Machines within the Virtual Machine Scale Set that powers this Node Pool. Possible values are Regular and Spot. Defaults to Regular.  
                                    When setting priority to Spot - you must configure an `eviction_policy` and `spot_max_price`.  
                                    Also the `node_labels` and `node_taints` needs to be added as per https://learn.microsoft.com/en-us/azure/aks/spot-node-pool.  
                                    Changing this forces a new resource to be created.

  `eviction_policy`              - (Optional) The Eviction Policy which should be used for Virtual Machines within the Virtual Machine Scale Set powering this Node Pool. Possible values are Deallocate and Delete. Changing this forces a new resource to be created.

  `spot_max_price`               - (Optional) The maximum price you're willing to pay in USD per Virtual Machine. Valid values are -1 (the current on-demand price for a Virtual Machine) or a positive value with up to five decimal places. Changing this forces a new resource to be created.

  `pod_subnet_id`                - (Optional) The ID of the Subnet where the pods in the Node Pool should exist. Changing this forces a new resource to be created.

  `max_pods`                     - (Optional) The maximum number of pods that can run on each agent. Changing this forces a new resource to be created.

  `node_labels`                  - (Optional) A map of Kubernetes labels which should be applied to nodes in this Node Pool.

  `node_taints`                  - (Optional) A list of Kubernetes taints which should be applied to nodes in the agent pool (e.g key=value:NoSchedule). Changing this forces a new resource to be created.

  CUSTOM NODE CONFIG

    File Handle Limits

    `custom_node_config_file_handle_max`  - (Optional) The sysctl setting fs.file-max. Must be between 8192 and 12000500. Changing this forces a new resource to be created.

    `custom_node_config_file_number_open` - (Optional) The sysctl setting fs.nr\_open. Must be between 8192 and 20000500. Changing this forces a new resource to be created.

    `custom_node_config_file_inotify_max` - (Optional) The sysctl setting fs.inotify.max\_user\_watches. Must be between 781250 and 2097152. Changing this forces a new resource to be created.

    `custom_node_config_file_aio_max`     - (Optional) The sysctl setting fs.aio-max-nr. Must be between 65536 and 6553500. Changing this forces a new resource to be created.

    Worker Limits

    `custom_node_config_kernel_threads_max` - (Optional) The sysctl setting kernel.threads-max. Must be between 20 and 513785. Changing this forces a new resource to be created.

    Socket and Network Tuning

      NET Core

      `custom_node_config_network_connection_max`                - (Optional) The sysctl setting net.core.somaxconn. Must be between 4096 and 3240000. Changing this forces a new resource to be created.

      `custom_node_config_network_dev_backlog_max`               - (Optional) The sysctl setting net.core.netdev\_max\_backlog. Must be between 1000 and 3240000. Changing this forces a new resource to be created.

      `custom_node_config_network_socket_receive_buffer_default` - (Optional) The sysctl setting net.core.rmem\_default. Must be between 212992 and 134217728. Changing this forces a new resource to be created.

      `custom_node_config_network_socket_receive_buffer_max`     - (Optional) The sysctl setting net.core.rmem\_max. Must be between 212992 and 134217728. Changing this forces a new resource to be created.

      `custom_node_config_network_socket_send_buffer_default`    - (Optional) The sysctl setting net.core.wmem\_default. Must be between 212992 and 134217728. Changing this forces a new resource to be created.

      `custom_node_config_network_socket_send_buffer_max`        - (Optional) The sysctl setting net.core.wmem\_max. Must be between 212992 and 134217728. Changing this forces a new resource to be created.

      `custom_node_config_network_socket_option_memory_max`      - (Optional) The sysctl setting net.core.optmem\_max. Must be between 20480 and 4194304. Changing this forces a new resource to be created.

      NET IPv4

      `custom_node_config_network_ipv4_connection_request_backlog_max` - (Optional) The sysctl setting net.ipv4.tcp\_max\_syn\_backlog. Must be between 128 and 3240000. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_timewait_bucket`                - (Optional) The sysctl setting net.ipv4.tcp\_max\_tw\_buckets. Must be between 8000 and 1440000. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_timewait_reuse`                 - (Optional) Is sysctl setting net.ipv4.tcp\_tw\_reuse enabled? Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_fin_timeout`                    - (Optional) The sysctl setting net.ipv4.tcp\_fin\_timeout. Must be between 5 and 120. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_tcp_keepalive_timeout`          - (Optional) The sysctl setting net.ipv4.tcp\_keepalive\_time. Must be between 30 and 432000. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_tcp_keepalive_probes`           - (Optional) The sysctl setting net.ipv4.tcp\_keepalive\_probes. Must be between 1 and 15. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_tcp_probe_interval`             - (Optional) The sysctl setting net.ipv4.tcp\_keepalive\_intvl. Must be between 10 and 90. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_local_port_range_min`           - (Optional) The sysctl setting net.ipv4.ip\_local\_port\_range min value. Must be between 1024 and 60999. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_local_port_range_max`           - (Optional) The sysctl setting net.ipv4.ip\_local\_port\_range max value. Must be between 32768 and 65535. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_arp_cache_gc_min`               - (Optional) The sysctl setting net.ipv4.neigh.default.gc\_thresh1. Must be between 128 and 80000. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_arp_cache_gc_soft_max`          - (Optional) The sysctl setting net.ipv4.neigh.default.gc\_thresh2. Must be between 512 and 90000. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_arp_cache_gc_max`               - (Optional) The sysctl setting net.ipv4.neigh.default.gc\_thresh3. Must be between 1024 and 100000. Changing this forces a new resource to be created.

      NET NetFilter

      `custom_node_config_network_ipv4_nat_connection_max` - (Optional) The sysctl setting net.netfilter.nf\_conntrack\_max. Must be between 131072 and 2097152. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_nat_bucket_max`     - (Optional) The sysctl setting net.netfilter.nf\_conntrack\_buckets. Must be between 65536 and 147456. Changing this forces a new resource to be created.

      Virtual Memory

      `custom_node_config_vm_max_map_count`       - (Optional) The sysctl setting vm.max\_map\_count. Must be between 65530 and 262144. Changing this forces a new resource to be created.

      `custom_node_config_vm_swappiness`          - (Optional) The sysctl setting vm.swappiness. Must be between 0 and 100. Changing this forces a new resource to be created.

      `custom_node_config_vm_vfs_cache_pressure`  - (Optional) The sysctl setting vm.vfs\_cache\_pressure. Must be between 0 and 100. Changing this forces a new resource to be created.

      Swap

      `custom_node_config_swap_file_size_mb` - (Optional) Specifies the size of swap file on each node in MB. Changing this forces a new resource to be created.

      Huge Pages

      `custom_node_config_transparent_huge_page_enabled` - (Optional) Specifies the Transparent Huge Page enabled configuration. Possible values are always, madvise and never. Changing this forces a new resource to be created.

      `custom_node_config_transparent_huge_page_defrag`  - (Optional) specifies the defrag configuration for Transparent Huge Page. Possible values are always, defer, defer+madvise, madvise and never. Changing this forces a new resource to be created.

  TIMEOUTS

  `timeout_create` - (Optional) Used when creating the Kubernetes Cluster Node Pool. Defaults to 30 minutes.

  `timeout_update` - (Optional) Used when updating the Kubernetes Cluster Node Pool. Defaults to 30 minutes.

  `timeout_read`   - (Optional) Used when retrieving the Kubernetes Cluster Node Pool. Defaults to 5 minutes.

  `timeout_delete` - (Optional) Used when deleting the Kubernetes Cluster Node Pool. Defaults to 30 minutes.

  TAGS

  `custom_tags` - (Optional) A mapping of custom tags which should be appended to the default tags.

Type:

```hcl
set(object({
    #General
    name               = string
    temporary_name_for_rotation = optional(string)
    mode               = optional(string, "User")
    kubernetes_version = optional(string)
    nodepool_subnet_id = optional(string)

    #Optional
    host_encryption_enabled  = optional(bool, true)
    node_public_ip_enabled   = optional(bool, false)
    node_public_ip_prefix_id = optional(string)
    fips_enabled             = optional(bool, false)
    workload_runtime         = optional(string, "OCIContainer")

    #Autoscaling
    auto_scaling_enabled = optional(bool, false)
    min_count            = optional(number)
    max_count            = optional(number)
    node_count           = optional(number)

    #Upgrade
    max_surge = optional(string, "100%")
    drain_timeout_in_minutes = optional(string, null)
    node_soak_duration_in_minutes = optional(string, null)

    #Nodes
    vm_size                      = string
    zones                        = optional(list(string))
    os_type                      = optional(string, "Linux")
    os_sku                       = optional(string)
    ultra_ssd_enabled            = optional(bool, false)
    os_disk_type                 = optional(string, "Managed")
    os_disk_size_gb              = optional(number)
    proximity_placement_group_id = optional(string)
    scale_down_mode              = optional(string, "Delete")
    priority                     = optional(string, "Regular")
    eviction_policy              = optional(string)
    spot_max_price               = optional(string)
    pod_subnet_id                = optional(string)
    max_pods                     = optional(number, 50)
    node_labels                  = optional(map(string))
    node_taints                  = optional(list(string))

    #Kubelet
    kubelet_disk_type                        = optional(string, "OS")
    kubelet_config_allowed_unsafe_sysctls    = optional(list(string), [])
    kubelet_config_container_log_max_line    = optional(number)
    kubelet_config_container_log_max_size_mb = optional(number)
    kubelet_config_cpu_manager_policy        = optional(string, "none")
    kubelet_config_cpu_cfs_quota_enabled     = optional(bool, false)
    kubelet_config_cpu_cfs_quota_period      = optional(string)
    kubelet_config_image_gc_high_threshold   = optional(number, 85)
    kubelet_config_image_gc_low_threshold    = optional(number, 80)
    kubelet_config_pod_max_pid               = optional(number)
    kubelet_config_topology_manager_policy   = optional(string, "none")

    #Custom Node Config
    #File Handle Limits
    custom_node_config_file_handle_max  = optional(number, 709620)
    custom_node_config_file_number_open = optional(number, 1048576)
    custom_node_config_file_inotify_max = optional(number, 1048576)
    custom_node_config_file_aio_max     = optional(number, 65536)
    #Worker Limits
    custom_node_config_kernel_threads_max = optional(number, 55601)
    #Socket and Network Tuning
    #net_core
    custom_node_config_network_connection_max                = optional(number, 16384)
    custom_node_config_network_dev_backlog_max               = optional(number, 1000)
    custom_node_config_network_socket_receive_buffer_default = optional(number, 212992)
    custom_node_config_network_socket_receive_buffer_max     = optional(number, 212992)
    custom_node_config_network_socket_send_buffer_default    = optional(number, 212992)
    custom_node_config_network_socket_send_buffer_max        = optional(number, 212992)
    custom_node_config_network_socket_option_memory_max      = optional(number, 20480)
    #net_ipv4
    custom_node_config_network_ipv4_connection_request_backlog_max = optional(number, 16384)
    custom_node_config_network_ipv4_timewait_bucket                = optional(number, 32768)
    custom_node_config_network_ipv4_timewait_reuse                 = optional(bool, false)
    custom_node_config_network_ipv4_fin_timeout                    = optional(number, 60)
    custom_node_config_network_ipv4_tcp_keepalive_timeout          = optional(number, 7200)
    custom_node_config_network_ipv4_tcp_keepalive_probes           = optional(number, 9)
    custom_node_config_network_ipv4_tcp_probe_interval             = optional(number, 75)
    custom_node_config_network_ipv4_local_port_range_min           = optional(number, 32768)
    custom_node_config_network_ipv4_local_port_range_max           = optional(number, 60999)
    custom_node_config_network_ipv4_arp_cache_gc_min               = optional(number, 4096)
    custom_node_config_network_ipv4_arp_cache_gc_soft_max          = optional(number, 8192)
    custom_node_config_network_ipv4_arp_cache_gc_max               = optional(number, 16384)
    #net_filter
    custom_node_config_network_ipv4_nat_connection_max = optional(number, 131072)
    custom_node_config_network_ipv4_nat_bucket_max     = optional(number, 65536)
    #virtual memory
    custom_node_config_vm_max_map_count      = optional(number, 65530)
    custom_node_config_vm_swappiness         = optional(number, 60)
    custom_node_config_vm_vfs_cache_pressure = optional(number, 100)
    #swap
    custom_node_config_swap_file_size_mb = optional(string)
    #huge pages
    custom_node_config_transparent_huge_page_enabled = optional(string)
    custom_node_config_transparent_huge_page_defrag  = optional(string)
    #Timeouts
    timeout_create = optional(string)
    timeout_update = optional(string)
    timeout_read   = optional(string)
    timeout_delete = optional(string)
    #Tags
    custom_tags = optional(map(string))
  }))
```

Default: `null`

### <a name="input_aks_nodepool_timeout_create"></a> [aks\_nodepool\_timeout\_create](#input\_aks\_nodepool\_timeout\_create)

Description: Used when creating the Kubernetes Cluster Node Pool. Defaults to 30 minutes.

Type: `string`

Default: `"30m"`

### <a name="input_aks_nodepool_timeout_delete"></a> [aks\_nodepool\_timeout\_delete](#input\_aks\_nodepool\_timeout\_delete)

Description: Used when deleting the Kubernetes Cluster Node Pool. Defaults to 30 minutes.

Type: `string`

Default: `"30m"`

### <a name="input_aks_nodepool_timeout_read"></a> [aks\_nodepool\_timeout\_read](#input\_aks\_nodepool\_timeout\_read)

Description: Used when retrieving the Kubernetes Cluster Node Pool. Defaults to 5 minutes.

Type: `string`

Default: `"5m"`

### <a name="input_aks_nodepool_timeout_update"></a> [aks\_nodepool\_timeout\_update](#input\_aks\_nodepool\_timeout\_update)

Description: Used when updating the Kubernetes Cluster Node Pool. Defaults to 30 minutes.

Type: `string`

Default: `"30m"`

### <a name="input_aks_oidc_issuer_enabled"></a> [aks\_oidc\_issuer\_enabled](#input\_aks\_oidc\_issuer\_enabled)

Description: (Optional) Enable or Disable the OIDC issuer URL. After enabling OIDC issuer on the cluster, it's not supported to disable it. Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_aks_outbound_type"></a> [aks\_outbound\_type](#input\_aks\_outbound\_type)

Description: (Optional) The outbound (egress) routing method which should be used for this Kubernetes Cluster. Possible values are loadBalancer, userDefinedRouting, managedNATGateway and userAssignedNATGateway. Defaults to userDefinedRouting. Changing this forces a new resource to be created.

Type: `string`

Default: `"userDefinedRouting"`

### <a name="input_aks_rg_name"></a> [aks\_rg\_name](#input\_aks\_rg\_name)

Description: (Optional) Name of the Azure Resource Group where the AKS created - will be automatically named if not provided

Type: `string`

Default: `null`

### <a name="input_aks_run_command_enabled"></a> [aks\_run\_command\_enabled](#input\_aks\_run\_command\_enabled)

Description: (Optional) Whether to enable run command for the cluster or not. Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_aks_tags"></a> [aks\_tags](#input\_aks\_tags)

Description: (Optional) Tags to applied to AKS resources

Type: `map(string)`

Default: `null`

### <a name="input_aks_version"></a> [aks\_version](#input\_aks\_version)

Description: (Optional) Expected Kubernetes version - if not provided, the latest will be used

Type: `string`

Default: `null`

### <a name="input_aks_workload_identity_enabled"></a> [aks\_workload\_identity\_enabled](#input\_aks\_workload\_identity\_enabled)

Description: (Optional) Specifies whether Azure AD Workload Identity should be enabled for the Cluster. AKS supports Microsoft Entra Workload ID on version 1.22 and higher. Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_alert_acr_StorageUsed_threshold"></a> [alert\_acr\_StorageUsed\_threshold](#input\_alert\_acr\_StorageUsed\_threshold)

Description: (Optional) Threshold for StorageUsed alert rule.

Type: `number`

Default: `483183820800`

### <a name="input_alert_cluster_autoscaler_unschedulable_pods_count_threshold"></a> [alert\_cluster\_autoscaler\_unschedulable\_pods\_count\_threshold](#input\_alert\_cluster\_autoscaler\_unschedulable\_pods\_count\_threshold)

Description: (Optional) Threshold for Cluster Autoscaler Unschedulable Pods Count alert rule.

Type: `number`

Default: `0`

### <a name="input_alert_kube_pod_status_phase_threshold"></a> [alert\_kube\_pod\_status\_phase\_threshold](#input\_alert\_kube\_pod\_status\_phase\_threshold)

Description: (Optional) Threshold for Pods Failed/Unknown state alert rule.

Type: `number`

Default: `0`

### <a name="input_alert_node_cpu_usage_percentage_threshold"></a> [alert\_node\_cpu\_usage\_percentage\_threshold](#input\_alert\_node\_cpu\_usage\_percentage\_threshold)

Description: (Optional) Threshold for Node CPU Usage Percentage alert rule.

Type: `number`

Default: `90`

### <a name="input_alert_node_disk_usage_percentage_threshold"></a> [alert\_node\_disk\_usage\_percentage\_threshold](#input\_alert\_node\_disk\_usage\_percentage\_threshold)

Description: (Optional) Threshold for Node CPU Usage Percentage alert rule.

Type: `number`

Default: `90`

### <a name="input_auto_scaler_profile"></a> [auto\_scaler\_profile](#input\_auto\_scaler\_profile)

Description: (Optional) Auto scaler profile configuration.

Type:

```hcl
object({
    balance_similar_node_groups      = optional(bool)
    expander                         = optional(string) // Possible values: least-waste, priority, most-pods, random
    max_graceful_termination_sec     = optional(number)
    max_node_provisioning_time       = optional(string)
    max_unready_nodes                = optional(number)
    max_unready_percentage           = optional(number)
    new_pod_scale_up_delay           = optional(string)
    scale_down_delay_after_add       = optional(string)
    scale_down_delay_after_delete    = optional(string)
    scale_down_delay_after_failure   = optional(string)
    scan_interval                    = optional(string)
    scale_down_unneeded              = optional(string)
    scale_down_unready               = optional(string)
    scale_down_utilization_threshold = optional(number)
    empty_bulk_delete_max            = optional(number)
    skip_nodes_with_local_storage    = optional(bool)
    skip_nodes_with_system_pods      = optional(bool)
  })
```

Default:

```json
{
  "balance_similar_node_groups": false,
  "empty_bulk_delete_max": 10,
  "expander": "random",
  "max_graceful_termination_sec": 600,
  "max_node_provisioning_time": "15m",
  "max_unready_nodes": 3,
  "max_unready_percentage": 45,
  "new_pod_scale_up_delay": "10s",
  "scale_down_delay_after_add": "10m",
  "scale_down_delay_after_delete": "10s",
  "scale_down_delay_after_failure": "3m",
  "scale_down_unneeded": "10m",
  "scale_down_unready": "20m",
  "scale_down_utilization_threshold": 0.5,
  "scan_interval": "10s",
  "skip_nodes_with_local_storage": true,
  "skip_nodes_with_system_pods": true
}
```

### <a name="input_builtin_metric_monitoring"></a> [builtin\_metric\_monitoring](#input\_builtin\_metric\_monitoring)

Description: (Optional) Set to false if default alerting rules are not required. Defaults to true

Type: `bool`

Default: `true`

### <a name="input_container_insights_configmap_filename"></a> [container\_insights\_configmap\_filename](#input\_container\_insights\_configmap\_filename)

Description: (Optional) Filename of configmap file which must be created along with Terraform files. It is mandatory in case container\_insights\_enable is true.

Type: `string`

Default: `null`

### <a name="input_container_insights_enable"></a> [container\_insights\_enable](#input\_container\_insights\_enable)

Description: (Optional) Enable container insights logging. Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_enable_acr_pull_policy"></a> [enable\_acr\_pull\_policy](#input\_enable\_acr\_pull\_policy)

Description: (Optional) Enable pull policy for the provided/integrated ACR. If switched off external policy required

Type: `bool`

Default: `true`

### <a name="input_enable_secret_rotation"></a> [enable\_secret\_rotation](#input\_enable\_secret\_rotation)

Description: (Optional) Secret CSI enable secret rotations

Type: `bool`

Default: `true`

### <a name="input_external_acr"></a> [external\_acr](#input\_external\_acr)

Description: (Optional) If you want to use existing ACR you need to pass its data object to this variable.

Type: `any`

Default: `null`

### <a name="input_http_proxy_override"></a> [http\_proxy\_override](#input\_http\_proxy\_override)

Description: (Optional) Override the default proxy server. The default reside in the convention module. Format: http://<server>:<port>/

Type: `string`

Default: `""`

### <a name="input_ingress_namespace"></a> [ingress\_namespace](#input\_ingress\_namespace)

Description: (Optional) Namespace of the ingress controller (only used by ingress-nginx)

Type: `string`

Default: `"ingress"`

### <a name="input_ingress_nginx_ip"></a> [ingress\_nginx\_ip](#input\_ingress\_nginx\_ip)

Description: (Conditionally required) IP Address of the loadbalancer for the Nginx Ingress controller. Required if ingress\_type = nginx

Type: `string`

Default: `"auto"`

### <a name="input_ingress_nginx_preserve_source_ip"></a> [ingress\_nginx\_preserve\_source\_ip](#input\_ingress\_nginx\_preserve\_source\_ip)

Description: (Optional) Set ingress controller.service.externalTrafficPolicy to Local

Type: `bool`

Default: `false`

### <a name="input_ingress_timeout"></a> [ingress\_timeout](#input\_ingress\_timeout)

Description: (Optional) Timeout for Ingress Nginx deployment in seconds.

Type: `number`

Default: `1200`

### <a name="input_ingress_type"></a> [ingress\_type](#input\_ingress\_type)

Description: (Optional) Type of the ingress controller preinstalled to the cluster: agic - Add-on based Azure Gateway Ingress Controller, nginx - Kubernetes Nginx Ingress Controller, none - no ingress controller installed

Type: `string`

Default: `"none"`

### <a name="input_insights_custom_data_flows"></a> [insights\_custom\_data\_flows](#input\_insights\_custom\_data\_flows)

Description: (Optional) Custom data flows for container insights.

Type:

```hcl
list(object({
    destinations = optional(list(string))
    streams = list(string)
    built_in_transform = optional(string)
    output_stream = optional(string)
    transform_kql = optional(string)
  }))
```

Default: `[]`

### <a name="input_istio_service_mesh_enabled"></a> [istio\_service\_mesh\_enabled](#input\_istio\_service\_mesh\_enabled)

Description: (Optional) Is Istio Service Mesh enabled. Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_key_vault_id"></a> [key\_vault\_id](#input\_key\_vault\_id)

Description: (Optional) Key Vault id for admin access token

Type: `string`

Default: `null`

### <a name="input_linux_admin_key"></a> [linux\_admin\_key](#input\_linux\_admin\_key)

Description: (Optional) SSH public key of the linux administrator, if provided. Disbled in production.

Type: `string`

Default: `null`

### <a name="input_linux_admin_usr"></a> [linux\_admin\_usr](#input\_linux\_admin\_usr)

Description: (Optional) Name of the linux administrator, if provided. Disbled in production.

Type: `string`

Default: `null`

### <a name="input_loganalytics"></a> [loganalytics](#input\_loganalytics)

Description:   (Optional) Logging configuration.  
    defender - (Optional) Specifies the ID of the Log Analytics Workspace where the audit logs collected by Microsoft Defender should be sent to.  
    oms      - (Optional) The ID of the Log Analytics Workspace which the OMS Agent should send data to. Defaults to the shared log analytics workspaces.  
    diag block supports the following attributes:  
      workspace\_id - (Optional) ID of target Log Analytics Workspace  
      log    - (Optional) List log types need to be sent to Log Analytics Workspace.  
      metric - (Optional) List metrics need to be sent to Log Analytics Workspace."

Type:

```hcl
object({
    defender = optional(string)
    oms      = optional(string)
    diag = optional(list(object({
      workspace_id = string
      log          = optional(list(string))
      metric       = optional(list(string))
    })))
  })
```

Default:

```json
{
  "defender": "",
  "diag": [
    {
      "log": [],
      "metric": [],
      "workspace_id": ""
    }
  ],
  "oms": ""
}
```

### <a name="input_no_proxy"></a> [no\_proxy](#input\_no\_proxy)

Description: (Optional) List of the addresses accessed without proxy. Azure own addresses automatically included. Changing it will force the AKS to recreate.

Type: `list(any)`

Default:

```json
[
  "otpnexus.hu",
  "otptesztnexus.hu",
  "otpbank.hu",
  "repsrv02lpr.kozpont.otp",
  "azurecr.io",
  "vault.azure.net"
]
```

### <a name="input_only_critical_addons_enabled"></a> [only\_critical\_addons\_enabled](#input\_only\_critical\_addons\_enabled)

Description: (Optional) Enabling this option will taint default node pool with CriticalAddonsOnly=true:NoSchedule taint. temporary\_name\_for\_rotation must be specified when changing this property.

Type: `bool`

Default: `false`

### <a name="input_open_service_mesh_enabled"></a> [open\_service\_mesh\_enabled](#input\_open\_service\_mesh\_enabled)

Description: (Optional) Is Open Service Mesh enabled? For more details, please visit Open Service Mesh for AKS. Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_pod_cidr"></a> [pod\_cidr](#input\_pod\_cidr)

Description: (Optional) The CIDR of the pod network.

Type: `string`

Default: `null`

### <a name="input_prometheus_enable"></a> [prometheus\_enable](#input\_prometheus\_enable)

Description: (Optional) Enable Prometheus installation

Type: `bool`

Default: `false`

### <a name="input_prometheus_namespace"></a> [prometheus\_namespace](#input\_prometheus\_namespace)

Description: (Optional) Namespace of the Prometheus

Type: `string`

Default: `"monitoring"`

### <a name="input_prometheus_timeout"></a> [prometheus\_timeout](#input\_prometheus\_timeout)

Description: (Optional) Timeout for Prometheus deployment in seconds.

Type: `number`

Default: `1200`

### <a name="input_resource_health_alert_location"></a> [resource\_health\_alert\_location](#input\_resource\_health\_alert\_location)

Description: (Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings.

Type: `string`

Default: `null`

### <a name="input_resource_health_monitoring"></a> [resource\_health\_monitoring](#input\_resource\_health\_monitoring)

Description: (Optional) Set to false if resource health alert rule is not required. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_secret_expiration_date"></a> [secret\_expiration\_date](#input\_secret\_expiration\_date)

Description: (Optional) Expiration date of the Kubernetes secrets stored in the key vault. Defaults to one year (it is acceptable for dev). On UAT, PPR and PRD environments this parameter needs to be set manually as expiration date should be less than 6 months.

Type: `string`

Default: `null`

### <a name="input_secret_rotation_interval"></a> [secret\_rotation\_interval](#input\_secret\_rotation\_interval)

Description: (Optional) Secret CSI secret rotation intervall

Type: `string`

Default: `"2m"`

### <a name="input_service_cidr"></a> [service\_cidr](#input\_service\_cidr)

Description: (Optional) The CIDR of the service network.

Type: `string`

Default: `null`

### <a name="input_service_mesh_external_ingress_gateway_enabled"></a> [service\_mesh\_external\_ingress\_gateway\_enabled](#input\_service\_mesh\_external\_ingress\_gateway\_enabled)

Description: (Optional) Is Istio External Ingress Gateway enabled? Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_service_mesh_internal_ingress_gateway_enabled"></a> [service\_mesh\_internal\_ingress\_gateway\_enabled](#input\_service\_mesh\_internal\_ingress\_gateway\_enabled)

Description: (Optional) Is Istio Internal Ingress Gateway enabled? Defaults to false.

Type: `bool`

Default: `false`

### <a name="input_service_mesh_revisions"></a> [service\_mesh\_revisions](#input\_service\_mesh\_revisions)

Description:     (Optional) Required when istio\_service\_mesh\_enabled is true.   
    Specify 1 or 2 Istio control plane revisions for managing minor upgrades using the canary upgrade process. For example, create the resource with revisions set to [asm-1-22], or leave it empty (the revisions will only be known after apply). To start the canary upgrade, change revisions to [asm-1-22, asm-1-23]. To roll back the canary upgrade, revert to [asm-1-22]. To confirm the upgrade, change to [asm-1-23].  
    Supported versions can be checked with following command: az aks mesh get-revisions --location westeurope -o table  
    Further documentation versioning: https://learn.microsoft.com/en-us/azure/aks/istio-support-policy

Type: `list(string)`

Default: `null`

### <a name="input_sku_tier"></a> [sku\_tier](#input\_sku\_tier)

Description: (Optional) The SKU Tier that should be used for this Kubernetes Cluster. Possible values are Free, Standard (which includes the Uptime SLA) and Premium. Defaults to Free on DEV and TST, Standard on PPR and PRD.

Type: `string`

Default: `null`

### <a name="input_storage_blob"></a> [storage\_blob](#input\_storage\_blob)

Description: (Optional) Enable Blob CSI driver - Needed for Storage Account Blob access

Type: `bool`

Default: `true`

### <a name="input_storage_disk"></a> [storage\_disk](#input\_storage\_disk)

Description: (Optional) Enable Disk CSI driver

Type: `bool`

Default: `true`

### <a name="input_storage_file"></a> [storage\_file](#input\_storage\_file)

Description: (Optional) Enable File CSI driver

Type: `bool`

Default: `true`

### <a name="input_storage_key_vault_id"></a> [storage\_key\_vault\_id](#input\_storage\_key\_vault\_id)

Description: (Optional) Id of the key vault where the storage account user managed key published. Kubelet need permission to read the key for decryption

Type: `string`

Default: `null`

### <a name="input_support_plan"></a> [support\_plan](#input\_support\_plan)

Description: (Optional) Specifies the support plan which should be used for this Kubernetes Cluster. Possible values are KubernetesOfficial and AKSLongTermSupport. Defaults to KubernetesOfficial.

Type: `string`

Default: `"KubernetesOfficial"`

### <a name="input_sys_pool_drain_timeout_in_minutes"></a> [sys\_pool\_drain\_timeout\_in\_minutes](#input\_sys\_pool\_drain\_timeout\_in\_minutes)

Description: (Optional) Drain timeout in minutes for system node pool

Type: `string`

Default: `null`

### <a name="input_sys_pool_enable_autoscaling"></a> [sys\_pool\_enable\_autoscaling](#input\_sys\_pool\_enable\_autoscaling)

Description: (Optional) System node pool autoscaling enable. Default: enabled.

Type: `bool`

Default: `true`

### <a name="input_sys_pool_max_pods"></a> [sys\_pool\_max\_pods](#input\_sys\_pool\_max\_pods)

Description: (Optional) Maximum number of pods per node. Default: 50

Type: `number`

Default: `50`

### <a name="input_sys_pool_max_surge"></a> [sys\_pool\_max\_surge](#input\_sys\_pool\_max\_surge)

Description: (Optional) Maximum surge for system node pool

Type: `string`

Default: `"100%"`

### <a name="input_sys_pool_node_count"></a> [sys\_pool\_node\_count](#input\_sys\_pool\_node\_count)

Description: (Optional) Initial number of nodes in the system node pool. Changing after creation require the autoscaling to be switched off.

Type: `number`

Default: `null`

### <a name="input_sys_pool_node_count_max"></a> [sys\_pool\_node\_count\_max](#input\_sys\_pool\_node\_count\_max)

Description: (Optional) Maximum number of nodes in the system node pool.

Type: `number`

Default: `1`

### <a name="input_sys_pool_node_count_min"></a> [sys\_pool\_node\_count\_min](#input\_sys\_pool\_node\_count\_min)

Description: (Optional) Minimum number of nodes in the system node pool.

Type: `number`

Default: `1`

### <a name="input_sys_pool_node_labels"></a> [sys\_pool\_node\_labels](#input\_sys\_pool\_node\_labels)

Description: (Optional) A map of Kubernetes labels which should be applied to nodes in the Default Node Pool.

Type: `map(string)`

Default: `null`

### <a name="input_sys_pool_node_size"></a> [sys\_pool\_node\_size](#input\_sys\_pool\_node\_size)

Description: (Optional) VM Istance type and size for the system node pool.

Type: `string`

Default: `"Standard_D4ds_v5"`

### <a name="input_sys_pool_node_soak_duration_in_minutes"></a> [sys\_pool\_node\_soak\_duration\_in\_minutes](#input\_sys\_pool\_node\_soak\_duration\_in\_minutes)

Description: (Optional) Node soak duration in minutes for system node pool

Type: `string`

Default: `null`

### <a name="input_sys_pool_os_disk_size"></a> [sys\_pool\_os\_disk\_size](#input\_sys\_pool\_os\_disk\_size)

Description: (Optional) The size of the OS Disk in GB which should be used for each agent in the Node Pool.

Type: `string`

Default: `null`

### <a name="input_sys_pool_zones"></a> [sys\_pool\_zones](#input\_sys\_pool\_zones)

Description: (Optional) Zones used for the system node pool node placement

Type: `list(string)`

Default: `null`

### <a name="input_sysctl_config"></a> [sysctl\_config](#input\_sysctl\_config)

Description: (Optional) Modify sysclt parameters for the system nodepool

Type:

```hcl
object({
    fs_aio_max_nr                      = optional(number)
    fs_file_max                        = optional(number)
    fs_inotify_max_user_watches        = optional(number)
    fs_nr_open                         = optional(number)
    kernel_threads_max                 = optional(number)
    net_core_netdev_max_backlog        = optional(number)
    net_core_optmem_max                = optional(number)
    net_core_rmem_default              = optional(number)
    net_core_rmem_max                  = optional(number)
    net_core_somaxconn                 = optional(number)
    net_core_wmem_default              = optional(number)
    net_core_wmem_max                  = optional(number)
    net_ipv4_ip_local_port_range_min   = optional(number)
    net_ipv4_ip_local_port_range_max   = optional(number)
    net_ipv4_neigh_default_gc_thresh1  = optional(number)
    net_ipv4_neigh_default_gc_thresh2  = optional(number)
    net_ipv4_neigh_default_gc_thresh3  = optional(number)
    net_ipv4_tcp_fin_timeout           = optional(number)
    net_ipv4_tcp_keepalive_intvl       = optional(number)
    net_ipv4_tcp_keepalive_probes      = optional(number)
    net_ipv4_tcp_keepalive_time        = optional(number)
    net_ipv4_tcp_max_syn_backlog       = optional(number)
    net_ipv4_tcp_max_tw_buckets        = optional(number)
    net_ipv4_tcp_tw_reuse              = optional(bool)
    net_netfilter_nf_conntrack_buckets = optional(number)
    net_netfilter_nf_conntrack_max     = optional(number)
    vm_max_map_count                   = optional(number)
    vm_swappiness                      = optional(number)
    vm_vfs_cache_pressure              = optional(number)
  })
```

Default:

```json
{
  "fs_aio_max_nr": null,
  "fs_file_max": null,
  "fs_inotify_max_user_watches": null,
  "fs_nr_open": null,
  "kernel_threads_max": null,
  "net_core_netdev_max_backlog": null,
  "net_core_optmem_max": null,
  "net_core_rmem_default": null,
  "net_core_rmem_max": null,
  "net_core_somaxconn": null,
  "net_core_wmem_default": null,
  "net_core_wmem_max": null,
  "net_ipv4_ip_local_port_range_max": null,
  "net_ipv4_ip_local_port_range_min": null,
  "net_ipv4_neigh_default_gc_thresh1": null,
  "net_ipv4_neigh_default_gc_thresh2": null,
  "net_ipv4_neigh_default_gc_thresh3": null,
  "net_ipv4_tcp_fin_timeout": null,
  "net_ipv4_tcp_keepalive_intvl": null,
  "net_ipv4_tcp_keepalive_probes": null,
  "net_ipv4_tcp_keepalive_time": null,
  "net_ipv4_tcp_max_syn_backlog": null,
  "net_ipv4_tcp_max_tw_buckets": null,
  "net_ipv4_tcp_tw_reuse": null,
  "net_netfilter_nf_conntrack_buckets": null,
  "net_netfilter_nf_conntrack_max": null,
  "vm_max_map_count": null,
  "vm_swappiness": null,
  "vm_vfs_cache_pressure": null
}
```

### <a name="input_temporary_name_for_rotation"></a> [temporary\_name\_for\_rotation](#input\_temporary\_name\_for\_rotation)

Description: (Optional) Specifies the name of the temporary node pool used to cycle the default node pool for VM resizing.

Type: `string`

Default: `"systemtmp"`

### <a name="input_use_proxy"></a> [use\_proxy](#input\_use\_proxy)

Description: (Optional) Use proxy server for the public internet access. Default: true

Type: `bool`

Default: `true`

## Outputs

The following outputs are exported:

### <a name="output_acre"></a> [acre](#output\_acre)

Description: n/a

### <a name="output_acre-password-vault-id"></a> [acre-password-vault-id](#output\_acre-password-vault-id)

Description: n/a

### <a name="output_acre-user"></a> [acre-user](#output\_acre-user)

Description: n/a

### <a name="output_aksc"></a> [aksc](#output\_aksc)

Description: n/a

### <a name="output_keyvault_identity"></a> [keyvault\_identity](#output\_keyvault\_identity)

Description: n/a

### <a name="output_nodepool_list"></a> [nodepool\_list](#output\_nodepool\_list)

Description: n/a

### <a name="output_rgrp"></a> [rgrp](#output\_rgrp)

Description: n/a

### <a name="output_umid-aks"></a> [umid-aks](#output\_umid-aks)

Description: n/a

### <a name="output_umid-kubelet"></a> [umid-kubelet](#output\_umid-kubelet)

Description: n/a

### <a name="output_validation_error"></a> [validation\_error](#output\_validation\_error)

Description: n/a

## Contributing

* If you think you've found a bug in the code or you have a question regarding
  the usage of this module, please reach out to <NAME_EMAIL>
* Contributions to this project are welcome: if you want to add a feature or a
  fix a bug, please do so by opening a Pull Request in this repository.
  In case of feature contribution, we kindly ask you to send a mail to
  discuss it beforehand.
<!-- END_TF_DOCS -->