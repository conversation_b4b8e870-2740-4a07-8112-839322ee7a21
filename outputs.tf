output "acre" {
  value = var.acr_enable ? module.acre.acre : null
}

output "acre-user" {
  value = var.acr_enable ? module.acre.acre-user : null
}

output "acre-password-vault-id" {
  value = var.acr_enable ? module.acre.acre-password-vault-id : null
}

output "aksc" {
  value = azurerm_kubernetes_cluster.aksc
}

output "rgrp" {
  value = var.aks_rg_name == null ? module.rgrp[0].rgrp : null
}

output "umid-aks" {
  value = azurerm_user_assigned_identity.umid-aks
}

output "umid-kubelet" {
  value = azurerm_user_assigned_identity.umid-kubelet
}

output "nodepool_list" {
  value = { for o in azurerm_kubernetes_cluster_node_pool.aks_nodepool : o.name => {
    name : o.name,
    id : o.id
  } }
}

output "keyvault_identity" {
  value = var.enable_secret_rotation == true ? azurerm_kubernetes_cluster.aksc.key_vault_secrets_provider[0].secret_identity[0] : null
}

output "validation_error" {  
  value       = local.validate_critical_addons_usage   
  sensitive   = true  
} 