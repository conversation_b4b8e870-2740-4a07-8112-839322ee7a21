# $$\   $$\  $$$$$$\  $$$$$$\ $$\   $$\ $$\   $$\ 
# $$$\  $$ |$$  __$$\ \_$$  _|$$$\  $$ |$$ |  $$ |
# $$$$\ $$ |$$ /  \__|  $$ |  $$$$\ $$ |\$$\ $$  |
# $$ $$\$$ |$$ |$$$$\   $$ |  $$ $$\$$ | \$$$$  / 
# $$ \$$$$ |$$ |\_$$ |  $$ |  $$ \$$$$ | $$  $$<  
# $$ |\$$$ |$$ |  $$ |  $$ |  $$ |\$$$ |$$  /\$$\ 
# $$ | \$$ |\$$$$$$  |$$$$$$\ $$ | \$$ |$$ /  $$ |
# \__|  \__| \______/ \______|\__|  \__|\__|  \__|
resource "tfcoremock_complex_resource" "ingress-nginx" {
  count = var.ingress_type == "nginx" ? 1 : 0
  depends_on = [
    module.nexus2acr
    , azurerm_kubernetes_cluster.aksc
    , azurerm_role_assignment.aks-current-rbac-admin-role-assignment
  ]
  lifecycle {
    replace_triggered_by = [
      azurerm_kubernetes_cluster.aksc
    ]
    ignore_changes = [
      id
    ]
  }
  map = {
    "aks_name" : {
      string = azurerm_kubernetes_cluster.aksc.name
    },
    "rg" : {
      string = var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name
    },
    "sp_id" : {
      string = data.azurerm_client_config.current.client_id
    },
    "tenant_id" : {
      string = data.azurerm_client_config.current.tenant_id
    },
    "subscription_id" : {
      string = data.azurerm_client_config.current.subscription_id
    },
    "ingress_namespace" : {
      string = var.ingress_namespace
    }
  }
  provisioner "local-exec" {
    interpreter = ["/bin/bash", "-c"]
    command     = <<EOT
        if [[ -f "$(pwd)/bin/linux_amd64/kubelogin" ]]
        then
            export PATH=$PATH:$(pwd)/bin/linux_amd64/
        fi
        if ! command -v kubelogin &> /dev/null
        then
            wget https://otpnexus.hu/repository/anonymous-proxy-ra-github.com/Azure/kubelogin/releases/download/v0.0.29/kubelogin-linux-amd64.zip --no-proxy
            unzip -n kubelogin-linux-amd64.zip
            export PATH=$PATH:$(pwd)/bin/linux_amd64/
        fi

        # Added retry logic to manage intermittent login issues
        if [ ! -z $ARM_CLIENT_SECRET ]
        then
          counter=0
          while [ $counter -lt 10 ]
          do                
              az login --service-principal -u ${data.azurerm_client_config.current.client_id} -p $ARM_CLIENT_SECRET --tenant ${data.azurerm_client_config.current.tenant_id}
              if [ $? -eq 0 ]
              then
                  echo 'SUCCESS: az login completed successfully.'
                  break
              fi

              echo 'az login failed. Retrying after 60 seconds.'
              sleep 60
              counter=$((counter + 1))
              if [ $counter -eq 9 ]
              then
                  echo 'ERROR: az login failed!'
              fi
          done
        fi

        # Added retry logic to manage delay in role assignment
        counter=0
        while [ $counter -lt 10 ]
        do
          az aks get-credentials --resource-group ${var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name} --name ${azurerm_kubernetes_cluster.aksc.name} --subscription ${data.azurerm_client_config.current.subscription_id} --overwrite-existing
          kubelogin convert-kubeconfig -l azurecli        
          helm upgrade ingress-nginx ${path.module}/helm/ingress-nginx/${local.ingress_helm_chart_version} --cleanup-on-fail -i --timeout ${var.ingress_timeout}s --wait \
            --create-namespace \
            -n ${var.ingress_namespace} \
            --set controller.image.registry=${local.container_registry} \
            --set controller.image.image=${local.ingress_images_repo_ingress} \
            --set controller.image.digest="" \
            --set controller.admissionWebhooks.patch.image.registry=${local.container_registry} \
            --set controller.admissionWebhooks.patch.image.image=${local.ingress_images_repo_certgen} \
            --set controller.admissionWebhooks.patch.image.digest="" \
            --set defaultBackend.image.registry=${local.container_registry} \
            --set defaultBackend.image.image=${local.ingress_images_repo_backend} \
            --set controller.service.loadBalancerIP=${var.ingress_nginx_ip} \
            --set controller.service.annotations.service\\.beta\\.kubernetes\\.io/azure-load-balancer-health-probe-request-path=/healthz \
            --set controller.service.annotations.service\\.beta\\.kubernetes\\.io/azure-load-balancer-internal=true \
            --set controller.ingressClassResource.default=true \
            --set controller.watchIngressWithoutClass=true \
            ${var.ingress_nginx_preserve_source_ip ? "--set controller.service.externalTrafficPolicy=Local" : ""}     
          if [ $? -eq 0 ]
          then
            echo 'SUCCESS: helm operation completed successfully.'
            break
          fi
          
          echo 'Helm operation failed. Retrying after 60 seconds.'
          sleep 60
          counter=$((counter + 1))
          if [ $counter -eq 9 ]
          then
            echo 'ERROR: Helm operation failed!'
          fi
        done        
    EOT
  }
  provisioner "local-exec" {
    when        = destroy
    interpreter = ["/bin/bash", "-c"]
    command     = <<EOT
      if [[ -f "$(pwd)/bin/linux_amd64/kubelogin" ]]
      then
        export PATH=$PATH:$(pwd)/bin/linux_amd64/
      fi    
      if ! command -v kubelogin &> /dev/null
      then
        wget https://otpnexus.hu/repository/anonymous-proxy-ra-github.com/Azure/kubelogin/releases/download/v0.0.29/kubelogin-linux-amd64.zip --no-proxy
        unzip -n kubelogin-linux-amd64.zip
        export PATH=$PATH:$(pwd)/bin/linux_amd64/
      fi
      if [ ! -z $ARM_CLIENT_SECRET ]
      then      
        az login --service-principal -u ${self.map.sp_id.string} -p $ARM_CLIENT_SECRET --tenant ${self.map.tenant_id.string}
      fi
      az aks get-credentials --resource-group ${self.map.rg.string} --name ${self.map.aks_name.string} --subscription ${self.map.subscription_id.string} --overwrite-existing
      kubelogin convert-kubeconfig -l azurecli
      helm status ingress-nginx -n ${self.map.ingress_namespace.string} && helm uninstall ingress-nginx --wait -n ${self.map.ingress_namespace.string} || true
    EOT
  }
}