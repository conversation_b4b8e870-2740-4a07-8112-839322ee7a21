# Configure the Microsoft Azure Provider
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
}

provider "tfcoremock" {
  use_only_state = true
}

provider "azurerm" {
  features {}
  alias = "logging"
  subscription_id = "a3a538d4-3d2b-4b05-b268-f147b8788b86"
}

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.10.0" //testing with minimum provider version
    }
    time = {
      source  = "hashicorp/time"
      version = "0.9.1"
    }
  }
}