variable "owner" {}
locals {
  resource_name = "aksdevops01"
}

module "conventions" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.1.0"
  cloud       = "azure"
  department  = "csdi"
  environment = "dev"
  region      = "westeurope"
  project     = "sbb"
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "false"
  }
}

data "azurerm_key_vault" "vault" {
  name                = "otp-dd-coeinfdev01"
  resource_group_name = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01"
}

data "azurerm_subnet" "snet-aks" {
  name                 = "aks"
  virtual_network_name = "otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01"
  resource_group_name  = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01" 
}

module "k8s" {
  source = "../.."
  // source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks?ref=v2.3.1"
  conventions              = module.conventions
  resource_name_suffix     = local.resource_name
  subnet_object            = data.azurerm_subnet.snet-aks
  use_proxy                = true
  linux_admin_usr          = "nodeadmin"
  linux_admin_key          = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC2HrLWQnkgaI3oyP/hBBKFpTFGgSvgdklW8/fB56oJVK2tUYdOJGpqKDFWzJMChmDYRSt9o7cJ3Gisfv+Urud2UjS+Gh0X7Vj1fycYnYnn6POr4mAnuIemhD/wrzJJzWj09vbdMRBuZRMGyQsxcVAExwnX0NtnY0F1LV7aIloWrZqSu7S/ft86HuTbrb3eMOctI1gV/noOlrVXUXnbMozEpKQtKese2vJgenOVZmxlVJpJkRZ1tyClETzLsSNU/5qNlkqGy9g+f2cNXjcVZUIlHpjR+SOtlnkmtl5nhCTaaofAmQtPwdh/UsKFAGfOqFzS+5J+NMabTpwjPDVsFOC9 rsa-key-20221202"
  sys_pool_node_size       = "Standard_D4ds_v5"
  sys_pool_node_count_max  = 3
  acr_generate_admin_token = true
  key_vault_id             = data.azurerm_key_vault.vault.id
  prometheus_enable        = false
  enable_acr_pull_policy   = false // disble integrated pull policy, external will be using (also include Nexus)
}

resource "azurerm_resource_policy_assignment" "image-source" {
    name                 = "image-source"
    resource_id          = module.k8s.aksc.id
    policy_definition_id = "/providers/Microsoft.Authorization/policyDefinitions/febd0533-8e55-448f-b837-bd0e06f16469"
    parameters = <<PARAMS
        {
            "effect": {
                "value": "audit"
                },
            "excludedNamespaces": {
                "value": [
                    "kube-system",
                    "gatekeeper-system"
                    ]
                },
            "allowedContainerImagesRegex": {
                "value": "${module.k8s.acre.name}\\.azurecr\\.io\\/.+|[a-z0-9-]+\\.otpnexus\\.hu\\/.+"
                }
        }
    PARAMS
}

