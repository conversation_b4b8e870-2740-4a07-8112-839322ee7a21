// Create resource group
locals {
  resource_name = "akstest11"
}

module "rg01" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.resource_name
}

// Create subnet in following VNET
data "azurerm_virtual_network" "vnet" {
  name                = var.vnet_vnet_name
  resource_group_name = var.vnet_vnet_rgrp_name
}

// Create an empty route table
module "rtbl_aks" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                        = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rtbl?ref=v1.3.0"
  conventions                   = module.conventions
  resource_name_suffix          = local.resource_name
  resource_group_name           = module.rg01.rgrp.name
  disable_bgp_route_propagation = true
}

module "subnet_tools" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                 = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/tooling//snet-tools?ref=v2.4.25"
  vnet_name              = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name         = data.azurerm_virtual_network.vnet.resource_group_name
  subnet_prefixes_length = [27]
}

// Create a subnet
module "subnet_aks" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                  = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-subnet?ref=v1.3.0"
  conventions             = module.conventions
  resource_name_suffix    = local.resource_name
  vnet_name               = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name          = data.azurerm_virtual_network.vnet.resource_group_name
  associated_route_table  = module.rtbl_aks.rtbl.id
  #address_prefix          = "************/27"
  address_prefix          = module.subnet_tools.next_subnets[0]
}

// Data resource is mandatory to get all information about subnet because resource does not provide information about route table
data "azurerm_subnet" "snet-aks" {
  name                 = module.subnet_aks.snet.name
  virtual_network_name = var.vnet_vnet_name
  resource_group_name  = var.vnet_vnet_rgrp_name

  depends_on = [ module.subnet_aks ]
}

module "k8s" {
  source                = "../.."
  // source             = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks?ref=v2.3.1"
  conventions           = module.conventions
  aks_rg_name           = module.rg01.rgrp.name
  resource_name_suffix  = local.resource_name
  subnet_object         = data.azurerm_subnet.snet-aks
  use_proxy             = true
  linux_admin_usr       = "nodeadmin"
  linux_admin_key       = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC2HrLWQnkgaI3oyP/hBBKFpTFGgSvgdklW8/fB56oJVK2tUYdOJGpqKDFWzJMChmDYRSt9o7cJ3Gisfv+Urud2UjS+Gh0X7Vj1fycYnYnn6POr4mAnuIemhD/wrzJJzWj09vbdMRBuZRMGyQsxcVAExwnX0NtnY0F1LV7aIloWrZqSu7S/ft86HuTbrb3eMOctI1gV/noOlrVXUXnbMozEpKQtKese2vJgenOVZmxlVJpJkRZ1tyClETzLsSNU/5qNlkqGy9g+f2cNXjcVZUIlHpjR+SOtlnkmtl5nhCTaaofAmQtPwdh/UsKFAGfOqFzS+5J+NMabTpwjPDVsFOC9 rsa-key-20221202"
  #prometheus_enable     = true

  sys_pool_node_size      = "Standard_D4ds_v5"
  sys_pool_node_count_max = 3

  #Below parameter needs to be used only if you need to deploy multiple AKS in the same environment with same project name
  acr_suffix = "11"

  loganalytics          = {
    defender = ""
    oms      = ""
    diag = []
  }

  #To shorten tests monitoring can be disabled here
  builtin_metric_monitoring = false
  resource_health_monitoring = false

  aks_nodepool = [
    {
      name                 = "testpool1"
      nodepool_subnet_id   = data.azurerm_subnet.snet-aks.id
      auto_scaling_enabled = false
      node_count           = 1 // needed when enable_auto_scaling = false
      vm_size  = "Standard_D4ds_v5"
      max_pods = 50
    }
  ]

}

