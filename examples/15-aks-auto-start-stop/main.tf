locals {
  suffix             = "aksstartstop01"
  resource_shortname = "lapw"
  short_region       = "weu"
  region             = "westeurope"
  environment        = "dev"
  project            = "aksstartstop01"
  //logic_app_name     = "${local.resource_shortname}-${var.conventions.short_region}-${var.conventions.environment}-${var.conventions.project}-01"
  logic_app_name    = "${local.resource_shortname}-${local.short_region}-${local.environment}-${local.project}"
  scheduled_hours   = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]
  scheduled_days    = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
  scheduled_minutes = [0]
}

module "rg" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.1.1"
  conventions          = module.conventions
  resource_name_suffix = "aks-start-stop01"
}

module "sql_api_conn" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source                   = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-logicapp//api-connection?ref=v1.3.0"
  conventions              = module.conventions
  resource_group_name      = module.rg.rgrp.name
  template_deployment_name = "deploy_api_connection01"
  template_body            = file("${path.module}/arm_templates/api_connection_template.json")
  parameters = jsonencode({
    connections_arm_name = { value = "arm" }
    location             = { value = module.rg.rgrp.location }
    subscription_id      = { value = data.azurerm_client_config.current.subscription_id }
  })
  depends_on = [module.rg]
}

resource "azurerm_resource_group_template_deployment" "deploy_logic_app" {
  name                = "deploy_logic_app01"
  resource_group_name = module.rg.rgrp.name
  template_content    = file("${path.module}/arm_templates/aks_startstop_template.json")

  parameters_content = jsonencode({
    name                 = { value = local.logic_app_name }
    location             = { value = module.rg.rgrp.location }
    resource_group_name  = { value = module.rg.rgrp.name }
    subscription_id      = { value = data.azurerm_client_config.current.subscription_id }
    connections_arm_name = { value = "arm" }
    scheduled_hours      = { value = local.scheduled_hours }
    scheduled_days       = { value = local.scheduled_days }
    scheduled_minutes    = { value = local.scheduled_minutes }
  })
  deployment_mode = "Incremental"
  depends_on = [module.rg,
    module.sql_api_conn
  ]
}
