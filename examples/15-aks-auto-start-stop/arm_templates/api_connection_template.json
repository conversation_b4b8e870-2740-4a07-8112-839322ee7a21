{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"connections_arm_name": {"defaultValue": "arm", "type": "String"}, "location": {"type": "String"}, "subscription_id": {"type": "String"}}, "variables": {}, "resources": [{"type": "Microsoft.Web/connections", "apiVersion": "2016-06-01", "name": "[parameters('connections_arm_name')]", "location": "[parameters('location')]", "kind": "V1", "properties": {"displayName": "aks", "statuses": [{"status": "Ready"}], "parameterValueType": "Alternative", "customParameterValues": {}, "createdTime": "2024-03-26T13:17:09.8957846Z", "changedTime": "2024-03-26T13:17:09.8957846Z", "api": {"name": "[parameters('connections_arm_name')]", "displayName": "Azure Resource Manager", "description": "Azure Resource Manager exposes the APIs to manage all of your Azure resources.", "iconUri": "[concat('https://connectoricons-prod.azureedge.net/releases/v1.0.1678/1.0.1678.3636/', parameters('connections_arm_name'), '/icon.png')]", "brandColor": "#003056", "id": "[concat('/subscriptions/', parameters('subscription_id'), '/providers/Microsoft.Web/locations/', parameters('location'), '/managedApis/', parameters('connections_arm_name'))]", "type": "Microsoft.Web/locations/managedApis"}, "testLinks": []}}]}