{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"name": {"defaultValue": "lapwe-weu-dev-aksstartstop01", "type": "String"}, "location": {"type": "String"}, "subscription_id": {"type": "String"}, "resource_group_name": {"type": "String"}, "connections_arm_name": {"type": "String"}, "scheduled_hours": {"defaultValue": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "type": "Array"}, "scheduled_days": {"defaultValue": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "Array"}, "scheduled_minutes": {"defaultValue": [0], "type": "Array"}}, "variables": {}, "resources": [{"type": "Microsoft.Logic/workflows", "apiVersion": "2017-07-01", "name": "[parameters('name')]", "location": "[parameters('location')]", "identity": {"type": "SystemAssigned"}, "properties": {"state": "Enabled", "definition": {"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "*******", "parameters": {"$connections": {"defaultValue": {}, "type": "Object"}, "subscription_id": {"defaultValue": "[parameters('subscription_id')]", "type": "String"}}, "triggers": {"Recurrence": {"recurrence": {"frequency": "Week", "interval": 1, "schedule": {"hours": "[parameters('scheduled_hours')]", "weekDays": "[parameters('scheduled_days')]", "minutes": "[parameters('scheduled_minutes')]"}}, "evaluatedRecurrence": {"frequency": "Hour", "interval": 1}, "type": "Recurrence"}}, "actions": {"AKS_Clusters": {"runAfter": {"List_resources_by_subscription": ["Succeeded"]}, "type": "Query", "inputs": {"from": "@body('List_resources_by_subscription')?['value']", "where": "@equals(item()?['type'], 'Microsoft.ContainerService/managedClusters')"}}, "AKS_Clusters_Loop": {"foreach": "@body('AKS_Clusters')", "actions": {"Is_Aks_Running": {"actions": {"Is_AKS_Scheduled_to_stop": {"actions": {"HTTP": {"type": "Http", "inputs": {"authentication": {"type": "ManagedServiceIdentity"}, "method": "POST", "uri": "https://management.azure.com@{items('AKS_Clusters_Loop')?['id']}/stop?api-version=2024-01-01"}}}, "else": {"actions": {"For_each_Nodepool": {"foreach": "@body('Parse_Nodepools')?['agentPoolProfiles']", "actions": {"Is_Nodepool_Running": {"actions": {"Nodepool_Scheduled_Stop": {"type": "Compose", "inputs": "@items('For_each_Nodepool')?['tags']?['nodepool-scheduled-stop']"}, "Should_Nodepool_be_stopped": {"actions": {"Generate_Nodepool_Stop_json_Payload": {"type": "Compose", "inputs": "@setProperty(body('Nodepool_Powerstate'),'powerState',setProperty(body('Nodepool_Powerstate')?['powerState'],'code','Stopped'))"}, "Stop_Agent_Pool": {"runAfter": {"Generate_Nodepool_Stop_json_Payload": ["Succeeded"]}, "type": "Http", "inputs": {"authentication": {"audience": "https://management.azure.com/", "type": "ManagedServiceIdentity"}, "body": {"properties": "@removeProperty(outputs('Generate_Nodepool_Stop_json_Payload'),'name')"}, "method": "PUT", "uri": "https://management.azure.com@{outputs('Resource_Id')}/agentPools/@{items('For_each_Nodepool')?['name']}?api-version=2024-01-01"}}}, "runAfter": {"Nodepool_Scheduled_Stop": ["Succeeded"]}, "else": {"actions": {}}, "expression": {"and": [{"equals": ["@outputs('Nodepool_Scheduled_Stop')", "@variables('currentTime')"]}]}, "type": "If"}}, "runAfter": {"isNodepoolRunning": ["Succeeded"]}, "else": {"actions": {"Nodepool_Scheduled_Start": {"type": "Compose", "inputs": "@items('For_each_Nodepool')?['tags']?['nodepool-scheduled-start']"}, "Should_Nodepool_be_started": {"actions": {"Generate_Nodepool_Start_json_Payload": {"type": "Compose", "inputs": "@setProperty(body('Nodepool_Powerstate'),'powerState',setProperty(body('Nodepool_Powerstate')?['powerState'],'code','Running'))"}, "Start_Agent_Pool": {"runAfter": {"Generate_Nodepool_Start_json_Payload": ["Succeeded"]}, "type": "Http", "inputs": {"authentication": {"type": "ManagedServiceIdentity"}, "body": {"properties": "@removeProperty(outputs('Generate_Nodepool_Start_json_Payload'),'name')"}, "method": "PUT", "uri": "https://management.azure.com@{outputs('Resource_Id')}/agentPools/@{items('For_each_Nodepool')?['name']}?api-version=2024-01-01"}}}, "runAfter": {"Nodepool_Scheduled_Start": ["Succeeded"]}, "else": {"actions": {}}, "expression": {"and": [{"equals": ["@outputs('Nodepool_Scheduled_Start')", "@variables('currentTime')"]}]}, "type": "If"}}}, "expression": {"and": [{"equals": ["@outputs('isNodepoolRunning')", "Running"]}]}, "type": "If"}, "Nodepool_Name": {"type": "Compose", "inputs": "@items('For_each_Nodepool')?['name']"}, "Nodepool_Powerstate": {"runAfter": {"Nodepool_Name": ["Succeeded"]}, "type": "<PERSON><PERSON><PERSON><PERSON>", "inputs": {"content": "@items('For_each_Nodepool')", "schema": {"properties": {"powerState": {"properties": {"code": {"type": "string"}}, "type": "object"}}, "type": "object"}}}, "isNodepoolRunning": {"runAfter": {"Nodepool_Powerstate": ["Succeeded"]}, "type": "Compose", "inputs": "@body('Nodepool_Powerstate')?['powerState']?['code']"}}, "runAfter": {"Parse_Nodepools": ["Succeeded"]}, "type": "Foreach"}, "Parse_Nodepools": {"type": "<PERSON><PERSON><PERSON><PERSON>", "inputs": {"content": "@body('Read_a_resource')?['properties']", "schema": {"properties": {"agentPoolProfiles": {"items": {"properties": {"name": {"type": "string"}, "tags": {"properties": {"nodepool-scheduled-start": {"type": "string"}, "nodepool-scheduled-stop": {"type": "string"}}, "type": "object"}}, "required": ["name"], "type": "object"}, "type": "array"}}, "type": "object"}}}}}, "expression": {"and": [{"equals": ["@if(empty(body('Read_a_resource')?['tags']?['aks-scheduled-stop']),'None',body('Read_a_resource')?['tags']?['aks-scheduled-stop'])", "@variables('currentTime')"]}]}, "type": "If"}}, "runAfter": {"isAKSRunning": ["Succeeded"]}, "else": {"actions": {"Is_AKS_Scheduled_to_start": {"actions": {"Start_AKS_Cluster": {"type": "Http", "inputs": {"authentication": {"type": "ManagedServiceIdentity"}, "method": "POST", "uri": "https://management.azure.com@{items('AKS_Clusters_Loop')?['id']}/start?api-version=2024-01-01"}}}, "else": {"actions": {}}, "expression": {"and": [{"equals": ["@if(empty(body('Read_a_resource')?['tags']?['aks-scheduled-start']),'None',body('Read_a_resource')?['tags']?['aks-scheduled-start'])", "@variables('currentTime')"]}]}, "type": "If"}}}, "expression": {"and": [{"equals": ["@outputs('isAKSRunning')", "Running"]}]}, "type": "If"}, "Parse_AKS_Powerstate": {"runAfter": {"Read_a_resource": ["Succeeded"]}, "type": "<PERSON><PERSON><PERSON><PERSON>", "inputs": {"content": "@body('Read_a_resource')?['properties']", "schema": {"properties": {"powerState": {"properties": {"code": {"type": "string"}}, "type": "object"}, "provisioningState": {"type": "string"}}, "type": "object"}}}, "Read_a_resource": {"runAfter": {"Short_Resource_Id": ["Succeeded"]}, "type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['arm_1']['connectionId']"}}, "method": "get", "path": "/subscriptions/@{encodeURIComponent(parameters('subscription_id'))}/resourcegroups/@{encodeURIComponent(outputs('Resource_Group'))}/providers/@{encodeURIComponent('Microsoft.ContainerService')}/@{encodeURIComponent(outputs('Short_Resource_Id'))}", "queries": {"x-ms-api-version": "2021-03-01"}}}, "Resource_Group": {"runAfter": {"Resource_Id": ["Succeeded"]}, "type": "Compose", "inputs": "@split(outputs('Resource_Id'),'/')[4]"}, "Resource_Id": {"type": "Compose", "inputs": "@items('AKS_Clusters_Loop')?['id']"}, "Short_Resource_Id": {"runAfter": {"Resource_Group": ["Succeeded"]}, "type": "Compose", "inputs": "@concat('managedClusters/',split(outputs('Resource_Id'),'/')[8])"}, "isAKSRunning": {"runAfter": {"Parse_AKS_Powerstate": ["Succeeded"]}, "type": "Compose", "inputs": "@body('Parse_AKS_Powerstate')?['powerState']?['code']"}}, "runAfter": {"AKS_Clusters": ["Succeeded"]}, "type": "Foreach"}, "Current_Time": {"runAfter": {}, "type": "InitializeVariable", "inputs": {"variables": [{"name": "currentTime", "type": "string", "value": "@{formatDateTime(convertFromUtc(utcNow(), 'Central Europe Standard Time'), 'HH')}"}]}}, "List_resources_by_subscription": {"runAfter": {"Current_Time": ["Succeeded"]}, "type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['arm_1']['connectionId']"}}, "method": "get", "path": "/subscriptions/@{encodeURIComponent(parameters('subscription_id'))}/resources", "queries": {"x-ms-api-version": "2016-06-01"}}}}, "outputs": {}}, "parameters": {"$connections": {"value": {"arm_1": {"connectionId": "[concat('/subscriptions/', parameters('subscription_id'),'/resourceGroups/', parameters('resource_group_name'), '/providers/Microsoft.Web/connections/', parameters('connections_arm_name'))]", "connectionName": "[parameters('connections_arm_name')]", "connectionProperties": {"authentication": {"type": "ManagedServiceIdentity"}}, "id": "[concat('/subscriptions/', parameters('subscription_id'),'/providers/Microsoft.Web/locations/', parameters('location'), '/managedApis/arm')]"}}}}}}]}