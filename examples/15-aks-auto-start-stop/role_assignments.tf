// Logic app workflow azure role assignments
resource "azurerm_role_assignment" "reader_for_lapw" {
  scope                = data.azurerm_subscription.primary.id
  role_definition_name = "Reader"
  principal_id         = data.azurerm_logic_app_workflow.lapw.identity[0].principal_id

  depends_on = [data.azurerm_logic_app_workflow.lapw]
}

resource "azurerm_role_assignment" "contributor_for_lapw" {
  scope                = data.azurerm_subscription.primary.id
  role_definition_name = "Contributor"
  principal_id         = data.azurerm_logic_app_workflow.lapw.identity[0].principal_id

  depends_on = [data.azurerm_logic_app_workflow.lapw]
}

resource "azurerm_role_assignment" "ask_cluster_admin_for_lapw" {
  scope                = data.azurerm_subscription.primary.id
  role_definition_name = "Azure Kubernetes Service RBAC Cluster Admin"
  principal_id         = data.azurerm_logic_app_workflow.lapw.identity[0].principal_id

  depends_on = [data.azurerm_logic_app_workflow.lapw]
}