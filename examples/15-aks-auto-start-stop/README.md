# AKS Ato Start/Stop using a Logic App
The purpose of this example is to provide a method, how entire AKS Clusters or only certain nodepools can be stopped/started in a scheduled way.
This example is going to deploy a Logic App along with an API Connection (arm).

# Usage scenarios
The Logic App runs hourly and scans all AKS clusters in the given subscription where it is deployed. It looks for the below tags both at AKS Cluster as well as at nodepool level.
The Logic App uses a recurrence trigger, which you can use to customize when the Logic App should be triggered.
```
  scheduled_hours      = [0,1,2,3,4,5,6,7,8,9,10,11,13,14,15,16,17,18,19,20,21,22,23]
  scheduled_days       = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
  scheduled_minutes    = [0]
```
Usage scenarios:
- The requirement was that the end users should be able to manually restart the stopped components and the automation should not stop it again on the same day.
- Another requirement was that we might need different schedule during the weekend. This can be achieved by triggering the Logic App only on certain days.

In general the correct behaviour can be achieved by adjusting the Logic App schedule together with the tags.

# Required tags
## AKS tags
AKS Tags should be set when you want an entire AKS Cluster to be stopped/started.
Be aware of the followings: 
- When private AKS Clusters are stopped, the API Server's private endpoint is destroyed and it will be recreated at startup time. This might cause IP Address changes.
- AKS tags can be changed only when the AKS Cluster is in running state.

The required tags are the followings.

```
aks-scheduled-start : [hour in cest 00-24]
aks-scheduled-stop	: [hour in cest 00-24]
```

## Nodepool tags
In case you don't want to stop the entire AKS Cluster, only the user nodepools, you can use the below nodepool tags.
Note that nodepool tags cannot be set through the portal, only via command line. One way to set them is using Azure Cli:
```
az aks nodepool update --resource-group rgrp-weu-dev-aks09 --cluster-name aksc-weu-dev-aks09 --name testpool1 --tags nodepool-scheduled-start=08 nodepool-scheduled-stop=18 --no-wait
```

The required tags are the followings:
```
nodepool-scheduled-start : [hour in cest 00-24]
nodepool-scheduled-stop  : [hour in cest 00-24]
```
