variable "owner" {}
module "conventions" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.1.0"
  cloud       = "azure"
  department  = "csdi"
  environment = "dev"
  region      = "westeurope"
  project     = "sbb"
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "false"
  }
}

locals {
  name_aksc = "aksc-weu-dev-akstest01"
}

data "azurerm_subnet" "sn_privateendpoint" {
  name = "privateendpoints"
  virtual_network_name = "otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01"
  resource_group_name  = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01" 
}

data "azurerm_kubernetes_cluster" "aksc" {
  name = local.name_aksc
  resource_group_name = "rgrp-weu-dev-akstest01"
}

module "stac" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  #checkov:skip=CKV2_AZURE_40: AAD authorization is not executable in case of nested storage modules, which is a requirement for omitting Shared Access Keys.  
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount?ref=v1.1.0"
  conventions              = module.conventions
  resource_group_name      = data.azurerm_kubernetes_cluster.aksc.node_resource_group
  resource_name_suffix     = "aksblob"
  subnet_id                = data.azurerm_subnet.sn_privateendpoint.id
// use this to create a standard blob storage (most common)
  account_tier             = "Standard"
  account_kind             = "StorageV2"
  subresource_list         = ["blob"]
  is_hns_enabled           = true
  account_replication_type = "ZRS"
  nfsv3_enabled            = true
}


resource "azurerm_storage_container" "stco" {
  #checkov:skip=CKV2_AZURE_21: AKS - Out of scope. Should be handled in the Storage Account Module. Jira CCE-2906
  depends_on = [
    module.stac
  ]
  name                  = "akscontainer"
  storage_account_name  = module.stac.stac.name
  container_access_type = "private"
}

/* This data is no longer stored in key vault from v1.3.0. This information can be fetched from data source: azurerm_kubernetes_cluster
data "azurerm_key_vault" "vault" {
  name                = "otp-dd-coeinfdev01"
  resource_group_name  = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01" 
}

data "azurerm_key_vault_secret" "cert-aksc-ca" {
  name         = "${local.name_aksc}-ca-certificate"
  key_vault_id = data.azurerm_key_vault.vault.id  
}

data "azurerm_key_vault_secret" "aksc-fqdn" {
  name         = "${local.name_aksc}-fqdn"
  key_vault_id = data.azurerm_key_vault.vault.id
}
*/


resource "helm_release" "storage-blob-nfs" {
  depends_on = [
    azurerm_storage_container.stco
  ]
  name    = "test-blob-nfs"
  timeout = 600
  chart   = "../../helm/test-blob-nfs"
  wait    = true
  namespace = "test"
  create_namespace = true
  set {
    name  = "storage.resourceGroup"
    value = data.azurerm_kubernetes_cluster.aksc.node_resource_group
  }
  set {
    name  = "storage.storageAccountName"
    value = module.stac.stac.name
  }
  set {
    name  = "storage.containerName"
    value = azurerm_storage_container.stco.name
  }
}