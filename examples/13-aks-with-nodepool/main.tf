variable "owner" {}
locals {
  resource_name = "akstest03"
}

module "conventions" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.1.0"
  cloud       = "azure"
  department  = "csdi"
  environment = "dev"
  region      = "westeurope"
  project     = "sbb"
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "false"
  }
}

// Data resource is mandatory to get all information about subnet because resource does not provide information about route table
data "azurerm_subnet" "snet-aks" {
  name                 = "aks"
  virtual_network_name = "otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01"
  resource_group_name  = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01" 
}

module "k8s" {
  source                = "../.."
  // source             = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks?ref=v2.3.1"
  conventions           = module.conventions
  resource_name_suffix  = local.resource_name
  subnet_object         = data.azurerm_subnet.snet-aks
  use_proxy             = true
  linux_admin_usr       = "nodeadmin"
  linux_admin_key       = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC2HrLWQnkgaI3oyP/hBBKFpTFGgSvgdklW8/fB56oJVK2tUYdOJGpqKDFWzJMChmDYRSt9o7cJ3Gisfv+Urud2UjS+Gh0X7Vj1fycYnYnn6POr4mAnuIemhD/wrzJJzWj09vbdMRBuZRMGyQsxcVAExwnX0NtnY0F1LV7aIloWrZqSu7S/ft86HuTbrb3eMOctI1gV/noOlrVXUXnbMozEpKQtKese2vJgenOVZmxlVJpJkRZ1tyClETzLsSNU/5qNlkqGy9g+f2cNXjcVZUIlHpjR+SOtlnkmtl5nhCTaaofAmQtPwdh/UsKFAGfOqFzS+5J+NMabTpwjPDVsFOC9 rsa-key-20221202"
  ingress_type          = "nginx"
  ingress_nginx_ip      = "**********"
  #prometheus_enable     = true

  sys_pool_node_size      = "Standard_D4ds_v4"
  sys_pool_node_count_max = 3

  // Below parameter needs to be used only if you need to deploy multiple AKS in the same environment with same project name
  acr_suffix = "01"

  loganalytics = {
    defender = ""
    oms      = ""
    diag = []
  }
  // Builtin monitoring can be disabled here
  //builtin_metric_monitoring = false
  //resource_health_monitoring = false

  builtin_metric_monitoring  = false
  resource_health_monitoring = false


aks_nodepool = [
    ##################################
    #### Pool1 - Complete Example ####
    ##################################
    {
      #### Timeouts####

      timeout_create = "20m"
      timeout_update = "20m"
      timeout_read   = "10m"
      timeout_delete = "20m"

      #### General ####

      name               = "testpool1"
      mode               = "User" #defaults to "User"
      #kubernetes_version = "1.25"
      nodepool_subnet_id = data.azurerm_subnet.snet-aks.id

      #### Optional ####

      # host_encryption_enabled   = true
      # node_public_ip_enabled    = true
      # node_public_ip_prefix_id  = ""
      # fips_enabled              = true
      # workload_runtime          = ""

      ### Scaling
      auto_scaling_enabled = true
      # min_count          = 1 #defaults to 0
      max_count            = 3
      # node_count         = 1 # needed when enable_auto_scaling = false

      ### Upgrade
      max_surge = "33%" #defaults to "1"

      ### Nodes
      vm_size = "Standard_D2s_v4" #"Standard_D2_v3"
      zones   = ["1", "2", "3"]
      # os_type                      = "Linux" #defaults to "Linux"
      # os_sku                         = "CBLMariner" #defaults to "Ubuntu", needed when os_type = "Linux"
      # ultra_ssd_enabled = true
      # os_disk_type                 = "Ephemeral"
      os_disk_size_gb = 31
      # proximity_placement_group_id = ""
      # scale_down_mode              = "Deallocate" #defaults to "Delete"

      # priority                     = "Spot" # defaults to "Regular"
      # eviction_policy              = "Delete" #needs priority = "Spot"
      # spot_max_price               = -1 #needs priority = "Spot"

      # pod_subnet_id                = #needs AzureCNI
      max_pods = 50
      # node_labels                  = {}
      # node_taints                  = []

      #### Kubelet Config ####

      # kubelet_disk_type      = "Temporary"
      # kubelet_config_enabled = true
      # kubelet_config = {
      #   # allowed_unsafe_sysctls =
      #   container_log_max_line    = 20000
      #   container_log_max_size_mb = 50
      #   # cpu_manager_policy =
      #   # cpu_cfs_quota_enabled =
      #   # cpu_cfs_quota_period =
      #   image_gc_high_threshold = 80
      #   image_gc_low_threshold  = 50
      #   pod_max_pid             = 5000
      #   topology_manager_policy = "single-numa-node"
      # }


      ### Linux OS Custom Node Configs
      #### File Handle Limits
      custom_node_config_file_handle_max  = 5676960
      custom_node_config_file_number_open = 8388608
      custom_node_config_file_inotify_max = 1562500
      custom_node_config_file_aio_max     = 524288
      #### Worker Limits
      custom_node_config_kernel_threads_max = 222404
      #### Socket and Network Tuning
      ##### net_core
      custom_node_config_network_connection_max                = 131072
      custom_node_config_network_dev_backlog_max               = 160000
      custom_node_config_network_socket_receive_buffer_default = 1703936
      custom_node_config_network_socket_receive_buffer_max     = 13631488
      custom_node_config_network_socket_send_buffer_default    = 1703936
      custom_node_config_network_socket_send_buffer_max        = 13631488
      custom_node_config_network_socket_option_memory_max      = 81920
      ##### net_ipv4
      custom_node_config_network_ipv4_connection_request_backlog_max = 131072
      custom_node_config_network_ipv4_timewait_bucket                = 262144
      custom_node_config_network_ipv4_timewait_reuse                 = true
      custom_node_config_network_ipv4_fin_timeout                    = 30
      custom_node_config_network_ipv4_tcp_keepalive_timeout          = 14400
      custom_node_config_network_ipv4_tcp_keepalive_probes           = 15
      custom_node_config_network_ipv4_tcp_probe_interval             = 45
      custom_node_config_network_ipv4_local_port_range_min           = 16384
      custom_node_config_network_ipv4_local_port_range_max           = 62500
      custom_node_config_network_ipv4_arp_cache_gc_min               = 16384
      custom_node_config_network_ipv4_arp_cache_gc_soft_max          = 32768
      custom_node_config_network_ipv4_arp_cache_gc_max               = 65536
      ##### net_netfilter
      custom_node_config_network_ipv4_nat_connection_max = 262144
      custom_node_config_network_ipv4_nat_bucket_max     = 131072
      #### Virtual Memory
      custom_node_config_vm_max_map_count      = 131060
      custom_node_config_vm_swappiness         = 10
      custom_node_config_vm_vfs_cache_pressure = 90
      #### SWAP
      # custom_node_config_swap_file_size_mb = 8192
      #### Transparent Huge Pages
      # custom_node_config_transparent_huge_page_enabled = "always"
      # custom_node_config_transparent_huge_page_defrag = "always"

    },
    #####################################
    #### Pool2 with basic parameters ####
    #####################################
    {
      name                 = "testpool2"
      nodepool_subnet_id   = data.azurerm_subnet.snet-aks.id
      auto_scaling_enabled = false
      node_count           = 1 #needed when enable_auto_scaling = false
      vm_size  = "Standard_D2s_v4"
      max_pods = 50
    }    
  ]

}

