locals {
  resource_name = "akstest0425"
}

// Create resource group
module "rg01" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.resource_name
}

// Create a subnet for user nodepool with same route table what is used for system nodepool
module "subnet_aks_usernodepool" {
  #checkov:skip=CKV_TF_1:We are using tags eg v0.1.2 instead of commit hash
  source                  = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-network//snet?ref=v1.2.5"
  conventions             = module.conventions
  resource_name_suffix    = local.resource_name
  vnet_name               = data.azurerm_virtual_network.vnet.name
  vnet_rgrp_name          = data.azurerm_virtual_network.vnet.resource_group_name
  associated_route_table  = data.azurerm_route_table.nodepool_rtbl.id
  address_prefix          = var.usernodepool_subnet_address_prefix
}

module "k8s" {
  source                = "../.."
  // source             = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks?ref=v2.3.1"
  conventions           = module.conventions
  resource_name_suffix  = local.resource_name
  aks_rg_name           = module.rg01.rgrp.name
  subnet_object         = data.azurerm_subnet.snet-aks
  use_proxy             = true
  linux_admin_usr       = "nodeadmin"
  linux_admin_key       = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC2HrLWQnkgaI3oyP/hBBKFpTFGgSvgdklW8/fB56oJVK2tUYdOJGpqKDFWzJMChmDYRSt9o7cJ3Gisfv+Urud2UjS+Gh0X7Vj1fycYnYnn6POr4mAnuIemhD/wrzJJzWj09vbdMRBuZRMGyQsxcVAExwnX0NtnY0F1LV7aIloWrZqSu7S/ft86HuTbrb3eMOctI1gV/noOlrVXUXnbMozEpKQtKese2vJgenOVZmxlVJpJkRZ1tyClETzLsSNU/5qNlkqGy9g+f2cNXjcVZUIlHpjR+SOtlnkmtl5nhCTaaofAmQtPwdh/UsKFAGfOqFzS+5J+NMabTpwjPDVsFOC9 rsa-key-20221202"
  #ingress_type          = "nginx"
  #ingress_nginx_ip      = "**********"
  #ingress_nginx_ip      = var.ingress_nginx_ip
  #prometheus_enable     = true

  sys_pool_node_size      = "Standard_D4ds_v4"
  sys_pool_node_count_max = 3

  // Below parameter needs to be used only if you need to deploy multiple AKS in the same environment with same project name
  acr_suffix = "0425"


  // Use below block if logging is not required
  loganalytics = {
    defender = ""
    oms      = ""
    diag = []
  }

  // Builtin monitoring can be disabled here
  builtin_metric_monitoring   = false
  resource_health_monitoring  = false

  // Enable workload identity
  // aks_workload_identity_enabled = true

  // Create a dedicated user node pool with minimal set of parameters - see example 13 for complete example
  aks_nodepool = [
    {
      name                 = "testpool1"
      auto_scaling_enabled = false
      node_count           = 1 // needed when enable_auto_scaling = false
      vm_size              = "Standard_D2s_v4"
      max_pods             = 15

      nodepool_subnet_id   = module.subnet_aks_usernodepool.snet.id
    }/*,
    {
      name                 = "testpool2"
      auto_scaling_enabled = false
      node_count           = 1 // needed when enable_auto_scaling = false
      vm_size              = "Standard_D2s_v4"
      max_pods             = 15

      nodepool_subnet_id   = module.subnet_aks_usernodepool.snet.id
    }   */
  ]

}


