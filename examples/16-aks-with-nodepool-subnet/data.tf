// Data resource is mandatory to get all information about subnet because resource does not provide information about route table. 
// Please be accurate with upper and lowercase letters.
data "azurerm_subnet" "snet-aks" {
  name                 = var.vnet_snet_name
  virtual_network_name = var.vnet_vnet_name
  resource_group_name  = var.vnet_vnet_rgrp_name
}

// Subnet for nodepool
// Create subnet in following VNET
data "azurerm_virtual_network" "vnet" {
  name                = var.vnet_vnet_name
  resource_group_name = var.vnet_vnet_rgrp_name
}

data "azurerm_route_table" "nodepool_rtbl" {
  name                = var.rtbl_name
  resource_group_name = var.rtbl_rg_name
}





