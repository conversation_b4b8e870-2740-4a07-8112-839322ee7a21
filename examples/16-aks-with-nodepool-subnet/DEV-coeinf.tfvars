//Conventions
cloud       = "azure"
environment = "dev"
region      = "westeurope"
project     = "abb"

//Vnet
vnet_snet_name = "aks"
vnet_vnet_name = "OTP-DD-COEINFDEV-sub-dev-01-vnet-westeu-01"
vnet_vnet_rgrp_name = "OTP-DD-COEINFDEV-sub-dev-01-rg-westeu-01"

rtbl_name     = "aks-route"
rtbl_rg_name  = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01"

usernodepool_subnet_address_prefix = "***********/27"
ingress_nginx_ip = "**********"