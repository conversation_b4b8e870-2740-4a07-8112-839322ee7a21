// Key vault to store encrpytion key
data "azurerm_subnet" "key-vault-subnet" {
  name                 = "privateendpoints"
  virtual_network_name = "otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01"
  resource_group_name  = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01"
}

module "mykeyvault01" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source              = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault?ref=v1.5.0"
  resource_group_name  = module.rg01.rgrp.name
  conventions          = module.conventions
  resource_name_suffix = local.resource_name
  subnet_id            = data.azurerm_subnet.key-vault-subnet.id
  sku_name             = "premium"

  purge_protection_enabled = true
  admin_ids = ["3bd65e1b-4483-4bc4-8aa0-e8d8c8c7406d"] // cleng group
  
  rbac_wait_time     = "250s"
  prep_wait_time     = "300s"
}

// Generate encryption key
resource "azurerm_key_vault_key" "aks-disk-key" {
  name         = "des-aks-key"
  key_vault_id = module.mykeyvault01.kvau.id
  key_type     = "RSA-HSM"
  key_size     = 4096
  expiration_date = timeadd(timestamp(), "8760h")

  key_opts = [
    "decrypt",
    "encrypt",
    "sign",
    "unwrapKey",
    "verify",
    "wrapKey",
  ]

  depends_on = [ module.mykeyvault01 ]
}

// Generate encryption set
resource "azurerm_disk_encryption_set" "aks-disk-set" {
  name                = "des-${local.resource_name}"
  resource_group_name = module.rg01.rgrp.name
  location            = module.conventions.region
  key_vault_key_id    = azurerm_key_vault_key.aks-disk-key.id

  identity {
    type = "SystemAssigned"
  }
}

resource "azurerm_role_assignment" "role-sp" {
  scope                = module.mykeyvault01.kvau.id
  role_definition_name = "Key Vault Crypto Officer"
  principal_id         = data.azurerm_client_config.current.object_id
}

resource "azurerm_role_assignment" "role-des" {
  scope                = module.mykeyvault01.kvau.id
  role_definition_name = "Key Vault Crypto Service Encryption User"
  principal_id         = azurerm_disk_encryption_set.aks-disk-set.identity.0.principal_id
}
