variable "owner" {}
locals {
  resource_name = "akscmk14"
}

module "conventions" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.1.0"
  cloud       = "azure"
  department  = "csdi"
  environment = "dev"
  region      = "westeurope"
  project     = "sbb"
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "false"
  }
}

// Create resource group
module "rg01" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.resource_name
}

module "k8s" {
  source                = "../.."
  // source             = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks?ref=v2.3.1"
  conventions           = module.conventions
  resource_name_suffix  = local.resource_name
  aks_rg_name           = module.rg01.rgrp.name
  subnet_object         = data.azurerm_subnet.snet-aks
  use_proxy             = true
  linux_admin_usr       = "nodeadmin"
  linux_admin_key       = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC2HrLWQnkgaI3oyP/hBBKFpTFGgSvgdklW8/fB56oJVK2tUYdOJGpqKDFWzJMChmDYRSt9o7cJ3Gisfv+Urud2UjS+Gh0X7Vj1fycYnYnn6POr4mAnuIemhD/wrzJJzWj09vbdMRBuZRMGyQsxcVAExwnX0NtnY0F1LV7aIloWrZqSu7S/ft86HuTbrb3eMOctI1gV/noOlrVXUXnbMozEpKQtKese2vJgenOVZmxlVJpJkRZ1tyClETzLsSNU/5qNlkqGy9g+f2cNXjcVZUIlHpjR+SOtlnkmtl5nhCTaaofAmQtPwdh/UsKFAGfOqFzS+5J+NMabTpwjPDVsFOC9 rsa-key-20221202"
  
  // Following options are disabled only to reduce testing time
  //ingress_type          = "nginx"
  //ingress_nginx_ip      = "**********"
  //prometheus_enable     = true

  aks_cmk_enabled                = true
  aks_disk_encryption_set_id = azurerm_disk_encryption_set.aks-disk-set.id

  sys_pool_node_size      = "Standard_D4ds_v4"
  sys_pool_node_count_max = 3

  // Below parameter needs to be used only if you need to deploy multiple AKS in the same environment with same project name
  acr_suffix = "01"

  loganalytics = {
    defender = ""
    oms      = ""
    diag = []
  }
  // Builtin monitoring can be disabled to reduce testing time
  builtin_metric_monitoring   = false
  resource_health_monitoring  = false

  // Create a dedicated user node pool with minimal set of parameters - see example 13 for complete example
  aks_nodepool = [
    {
      name                 = "testpool1"
      nodepool_subnet_id   = data.azurerm_subnet.snet-aks.id
      auto_scaling_enabled = false
      node_count           = 1 // needed when enable_auto_scaling = false
      vm_size  = "Standard_D4ds_v4"
      max_pods = 50
    }
  ]

}


