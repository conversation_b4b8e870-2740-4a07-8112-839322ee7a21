variable "owner" {}
module "conventions" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.1.0"
  cloud       = "azure"
  department  = "csdi"
  environment = "dev"
  region      = "westeurope"
  project     = "sbb"
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "false"
  }
}

locals {
  name_aksc = "aksc-weu-dev-akstest01"
}

data "azurerm_subnet" "sn_privateendpoint" {
  name = "privateendpoints"
  virtual_network_name = "otp-dd-coeinfdev-sub-dev-01-vnet-westeu-01"
  resource_group_name  = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01" 
}

data "azurerm_kubernetes_cluster" "aksc" {
  name = local.name_aksc
  resource_group_name = "rgrp-weu-dev-akstest01"
}

module "stac" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  #checkov:skip=CKV_AZURE_3: Storage Account azurefiles nfs mount not compatible with enable_https_traffic_only
  #checkov:skip=CKV2_AZURE_40: AAD authorization is not executable in case of nested storage modules, which is a requirement for omitting Shared Access Keys.  
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-storageaccount?ref=v1.1.0"
  conventions              = module.conventions
  resource_group_name      = data.azurerm_kubernetes_cluster.aksc.node_resource_group
  resource_name_suffix     = "aksfile"
  subnet_id                = data.azurerm_subnet.sn_privateendpoint.id
  wait_after               = 600
// use this to create a standard file storage (most common)
  account_tier             = "Premium"
  account_kind             = "FileStorage"
  subresource_list         = ["file"]
  account_replication_type = "ZRS"
  large_file_share_enabled = true
  enable_https_traffic_only = false
}

resource "azurerm_storage_share" "nfs_share" {
  depends_on = [
    module.stac
  ]
  name                 = "aksnfsshare"
  storage_account_name = module.stac.stac.name
  quota                = 100
  enabled_protocol     = "NFS"
}
/* This data is no longer stored in key vault from v1.3.0. This information can be fetched from data source: azurerm_kubernetes_cluster
data "azurerm_key_vault" "vault" {
  name                = "otp-dd-coeinfdev01"
  resource_group_name  = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01" 
}

data "azurerm_key_vault_secret" "cert-aksc-ca" {
  name         = "${local.name_aksc}-ca-certificate"
  key_vault_id = data.azurerm_key_vault.vault.id  
}

data "azurerm_key_vault_secret" "aksc-fqdn" {
  name         = "${local.name_aksc}-fqdn"
  key_vault_id = data.azurerm_key_vault.vault.id
}
*/
// Test Helm Release
resource "helm_release" "storage-file-nfs" {
  depends_on = [
    azurerm_storage_share.nfs_share
  ]
  name    = "test-file-nfs"
  timeout = 600
  chart   = "../../helm/test-file-nfs"
  wait    = true
  namespace = "test"
  create_namespace = true
  set {
    name  = "storage.resourceGroup"
    value = data.azurerm_kubernetes_cluster.aksc.node_resource_group
  }
  set {
    name  = "storage.storageAccountName"
    value = module.stac.stac.name
  }
  set {
    name  = "storage.shareName"
    value = azurerm_storage_share.nfs_share.name
  }
  set {
    name  = "storage.key"
    value = module.stac.stac.primary_access_key
  }
}