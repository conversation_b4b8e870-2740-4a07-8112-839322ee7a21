variable "owner" {}
module "conventions" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.1.0"
  cloud       = "azure"
  department  = "csdi"
  environment = "dev"
  region      = "westeurope"
  project     = "sbb"
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "false"
  }
}

data "azurerm_client_config" "current" {
}

locals {
  aks_name = "aksc-weu-dev-akstest01"
  rg = "rgrp-weu-dev-akstest01"
}

resource "tfcoremock_complex_resource" "azuredisk-test" {
  map = {
    "aks_name" : {
      string = local.aks_name
    },
    "rg" : {
      string = local.rg
    },
    "sp_id" : {
      string = data.azurerm_client_config.current.client_id
    },
    "tenant_id" : {
      string = data.azurerm_client_config.current.tenant_id
    }
  }
  provisioner "local-exec" {
    command =  <<EOT
      if ! command -v kubelogin &> /dev/null
      then
        wget https://otpnexus.hu/repository/anonymous-proxy-ra-github.com/Azure/kubelogin/releases/download/v0.0.29/kubelogin-linux-amd64.zip
        unzip kubelogin-linux-amd64.zip
        export PATH=$PATH:$(pwd)/bin/linux_amd64/
      fi
      az login --service-principal -u ${data.azurerm_client_config.current.client_id} -p $ARM_CLIENT_SECRET --tenant ${data.azurerm_client_config.current.tenant_id}
      az aks get-credentials --resource-group ${local.rg} --name ${local.aks_name}
      kubelogin convert-kubeconfig -l azurecli
      helm upgrade azuredisk-test ../../helm/test-disk --cleanup-on-fail --create-namespace -i --timeout 600s --wait -n test
    EOT
  }
  provisioner "local-exec" {
    when = destroy
    command = <<EOT
      if ! command -v kubelogin &> /dev/null
      then
        wget https://otpnexus.hu/repository/anonymous-proxy-ra-github.com/Azure/kubelogin/releases/download/v0.0.29/kubelogin-linux-amd64.zip
        unzip kubelogin-linux-amd64.zip
        export PATH=$PATH:$(pwd)/bin/linux_amd64/
      fi
      az login --service-principal -u ${self.map.sp_id.string} -p $ARM_CLIENT_SECRET --tenant ${self.map.tenant_id.string}
      az aks get-credentials --resource-group ${self.map.rg.string} --name ${self.map.aks_name.string}
      kubelogin convert-kubeconfig -l azurecli
      helm uninstall azuredisk-test --wait -n test
    EOT
  }
}