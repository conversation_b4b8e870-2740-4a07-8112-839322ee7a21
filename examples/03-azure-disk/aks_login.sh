#!/bin/bash
SP_ID="$1"
TENANT_ID="$2"
if ! command -v kubelogin &> /dev/null
then
    wget https://otpnexus.hu/repository/anonymous-proxy-ra-github.com/Azure/kubelogin/releases/download/v0.0.29/kubelogin-linux-amd64.zip -q
    unzip -o -qq kubelogin-linux-amd64.zip
    export PATH=$PATH:$(pwd)/bin/linux_amd64/
fi
kubelogin get-token \
    --environment AzurePublicCloud \
    --server-id 6dae42f8-4368-4678-94ff-3960e28e3630 \
    --client-id $SP_ID \
    --client-secret $ARM_CLIENT_SECRET \
    --tenant-id $TENANT_ID \
    --login spn