locals {
  resource_name = "akstest12"
}

// Create resource group
module "rg01" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.resource_name
}

module "laws" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-loganalytics//la-workspace?ref=v1.4.0"
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  resource_name_suffix = local.resource_name

// To connect log analytics workspace to central AMPLS:
// 1) use below parameter
// 2) add an additional provider with alias=logging which points to OTP-MGM-Logging-sub-prd-01 subscription (see provider.tf in this example)
// 3) Pass azurerm.loging provider to the module
  ampls_scope_name          = "mpls-weu-prd-central-01"
  providers = {
    azurerm.log = azurerm.logging
  }
}

module "k8s" {
  source                = "../.."
  // source             = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-aks?ref=v2.3.1"
  conventions           = module.conventions
  aks_rg_name           = module.rg01.rgrp.name
  resource_name_suffix  = local.resource_name
  subnet_object         = data.azurerm_subnet.snet-aks
  use_proxy             = true
  linux_admin_usr       = "nodeadmin"
  linux_admin_key       = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC2HrLWQnkgaI3oyP/hBBKFpTFGgSvgdklW8/fB56oJVK2tUYdOJGpqKDFWzJMChmDYRSt9o7cJ3Gisfv+Urud2UjS+Gh0X7Vj1fycYnYnn6POr4mAnuIemhD/wrzJJzWj09vbdMRBuZRMGyQsxcVAExwnX0NtnY0F1LV7aIloWrZqSu7S/ft86HuTbrb3eMOctI1gV/noOlrVXUXnbMozEpKQtKese2vJgenOVZmxlVJpJkRZ1tyClETzLsSNU/5qNlkqGy9g+f2cNXjcVZUIlHpjR+SOtlnkmtl5nhCTaaofAmQtPwdh/UsKFAGfOqFzS+5J+NMabTpwjPDVsFOC9 rsa-key-20221202"
  //ingress_type          = "nginx"
  //ingress_nginx_ip      = "**********"
  #prometheus_enable     = true

  sys_pool_node_size      = "Standard_D4ds_v5"
  sys_pool_node_count_max = 3

  #Below parameter needs to be used only if you need to deploy multiple AKS in the same environment with same project name
  acr_suffix = local.resource_name

  loganalytics = {
    defender = ""
    oms      = module.laws.laws.id
    diag = []
  }

  #To shorten tests monitoring can be disabled here
  builtin_metric_monitoring = false
  resource_health_monitoring = false

  # Configure container insights
  container_insights_enable = true
  container_insights_configmap_filename = "ama-logs-rs-config_v2.yaml"
}

