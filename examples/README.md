### 01-default
Deploy an AKS cluster with ingress-nginx and Prometheus
### 02-aks2
Deploy an AKS cluster - secondary cluster for SBB testing purposes only
### 03-azure-disk
Deploy an nginx with Azure Disk connected. Access method for the AKS cluster use information from the azurerem_kubernetes_cluster data gather. Doesn't contain the cluster itself
### 04-storage-blob-nfs
Deploy an nginx with Storage Account blob - nfs connected. Access method for the AKS cluster use information from Key Vault exclusively. Doesn't contain the cluster itself
### 05-storage-azurefiles
Deploy an nginx with Storage Account azurefiles - nfs connected. Access method for the AKS cluster use information from Key Vault exclusively. Doesn't contain the cluster itself
### 06-azure-disk-np
Deploy an nginx with Azure Disk connected. Doesn't use TF Helm provider. Doesn't contain the cluster itself
### 07-create-namespace
Use of TF Kubernetes provider. Access method for the AKS cluster use information from Key Vault exclusively. Doesn't contain the cluster itself
### 08-devops-aks
Deploy AKS cluster to the devops subnet. It use external pull policy for nexus. (This example not yet complette, CA certificate injection is missing.) It require CustomCATrustPreview enabled in Microsoft.ContainerService namespace at subscription level.