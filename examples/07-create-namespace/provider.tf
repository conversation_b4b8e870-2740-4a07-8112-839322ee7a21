# Configure the Microsoft Azure Provider
provider "azurerm" {
  features {}
}

provider "kubernetes" {
  host                   = data.azurerm_kubernetes_cluster.aksc.kube_config[0].host
  cluster_ca_certificate = base64decode(data.azurerm_kubernetes_cluster.aksc.kube_config[0].cluster_ca_certificate)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    args = [
      "./aks_login.sh",
      "e153c748-5cc7-4d66-86d9-d6acbb84bdc6",
      "60c1c779-9336-42ce-8e98-772a5e8de926"
    ]
    command = "bash"
  }
}
