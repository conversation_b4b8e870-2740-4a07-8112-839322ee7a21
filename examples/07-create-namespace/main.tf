variable "owner" {}
module "conventions" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.1.0"
  cloud       = "azure"
  department  = "csdi"
  environment = "dev"
  region      = "westeurope"
  project     = "sbb"
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "false"
  }
}

locals {
  name_aksc = "aksc-weu-dev-akstest01"
}

/* This data is no longer stored in key vault from v1.3.0. This information can be fetched from data source: azurerm_kubernetes_cluster
data "azurerm_key_vault" "vault" {
  name                = "otp-dd-coeinfdev01"
  resource_group_name = "otp-dd-coeinfdev-sub-dev-01-rg-westeu-01"
}

data "azurerm_kubernetes_cluster" "aksc" {
  name = local.name_aksc
  resource_group_name = "rgrp-weu-dev-akstest01"
}

data "azurerm_key_vault_secret" "cert-aksc-ca" {
  name         = "${local.name_aksc}-ca-certificate"
  key_vault_id = data.azurerm_key_vault.vault.id  
}

data "azurerm_key_vault_secret" "aksc-fqdn" {
  name         = "${local.name_aksc}-fqdn"
  key_vault_id = data.azurerm_key_vault.vault.id
}
*/

resource "kubernetes_namespace" "ns-dev" {
  metadata {
    name = "dev"
  }
}
