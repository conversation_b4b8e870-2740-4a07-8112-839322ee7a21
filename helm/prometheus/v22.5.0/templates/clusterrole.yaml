{{- if and .Values.rbac.create (empty .Values.server.useExistingClusterRoleName) -}}
apiVersion: {{ template "rbac.apiVersion" . }}
kind: ClusterRole
metadata:
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
  name: {{ include "prometheus.clusterRoleName" . }}
rules:
{{- if .Values.podSecurityPolicy.enabled }}
  - apiGroups:
    - extensions
    resources:
    - podsecuritypolicies
    verbs:
    - use
    resourceNames:
    - {{ template "prometheus.server.fullname" . }}
{{- end }}
  - apiGroups:
      - ""
    resources:
      - nodes
      - nodes/proxy
      - nodes/metrics
      - services
      - endpoints
      - pods
      - ingresses
      - configmaps
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - "extensions"
      - "networking.k8s.io"
    resources:
      - ingresses/status
      - ingresses
    verbs:
      - get
      - list
      - watch
  - nonResourceURLs:
      - "/metrics"
    verbs:
      - get
{{- end }}
