{{- if and .Values.rbac.create (empty .Values.server.namespaces) (empty .Values.server.useExistingClusterRoleName) -}}
apiVersion: {{ template "rbac.apiVersion" . }}
kind: ClusterRoleBinding
metadata:
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
  name: {{ include "prometheus.clusterRoleName" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "prometheus.serviceAccountName.server" . }}
    namespace: {{ include "prometheus.namespace" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "prometheus.clusterRoleName" . }}
{{- end }}
