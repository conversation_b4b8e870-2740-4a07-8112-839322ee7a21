apiVersion: v2
name: prometheus
appVersion: v2.43.1
version: 22.5.0
kubeVersion: ">=1.16.0-0"
description: Prometheus is a monitoring system and time series database.
home: https://prometheus.io/
icon: https://raw.githubusercontent.com/prometheus/prometheus.github.io/master/assets/prometheus_logo-cb55bb5c346.png
sources:
  - https://github.com/prometheus/alertmanager
  - https://github.com/prometheus/prometheus
  - https://github.com/prometheus/pushgateway
  - https://github.com/prometheus/node_exporter
  - https://github.com/kubernetes/kube-state-metrics
maintainers:
  - name: gian<PERSON><PERSON>
    email: gianru<PERSON>@gmail.com
  - name: zanhsieh
    email: <EMAIL>
  - name: Xtigyro
    email: <EMAIL>
  - name: naseemkullah
    email: <EMAIL>
  - name: zeritti
    email: <EMAIL>
type: application
dependencies:
  - name: alertmanager
    version: "0.30.*"
    repository: https://prometheus-community.github.io/helm-charts
    condition: alertmanager.enabled
  - name: kube-state-metrics
    version: "4.30.*"
    repository: https://prometheus-community.github.io/helm-charts
    condition: kube-state-metrics.enabled
  - name: prometheus-node-exporter
    version: "4.8.*"
    repository: https://prometheus-community.github.io/helm-charts
    condition: prometheus-node-exporter.enabled
  - name: prometheus-pushgateway
    version: "2.0.*"
    repository: https://prometheus-community.github.io/helm-charts
    condition: prometheus-pushgateway.enabled
keywords:
  - monitoring
  - prometheus
annotations:
  "artifacthub.io/license": Apache-2.0
  "artifacthub.io/links": |
    - name: Chart Source
      url: https://github.com/prometheus-community/helm-charts
    - name: Upstream Project
      url: https://github.com/prometheus/prometheus
