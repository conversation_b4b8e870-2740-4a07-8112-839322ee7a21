{{- if .Values.networkPolicy.enabled }}
apiVersion: {{ template "prometheus.networkPolicy.apiVersion" . }}
kind: NetworkPolicy
metadata:
  name: {{ template "prometheus.server.fullname" . }}
  namespace: {{ include "prometheus.namespace" . }}
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "prometheus.server.matchLabels" . | nindent 6 }}
  ingress:
    - ports:
      - port: 9090
{{- end }}
