{{- if and .Values.rbac.create .Values.podSecurityPolicy.enabled }}
{{- if .Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy" }}
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: {{ template "prometheus.server.fullname" . }}
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
  {{- with .Values.server.podSecurityPolicy.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  privileged: false
  allowPrivilegeEscalation: false
  allowedCapabilities:
    - 'CHOWN'
  volumes:
    - 'configMap'
    - 'persistentVolumeClaim'
    - 'emptyDir'
    - 'secret'
    - 'hostPath'
  allowedHostPaths:
    - pathPrefix: /etc
      readOnly: true
    - pathPrefix: {{ .Values.server.persistentVolume.mountPath }}
  {{- range .Values.server.extraHostPathMounts }}
    - pathPrefix: {{ .hostPath }}
      readOnly: {{ .readOnly }}
  {{- end }}
  hostNetwork: false
  hostPID: false
  hostIPC: false
  runAsUser:
    rule: 'RunAsAny'
  seLinux:
    rule: 'RunAsAny'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      # Forbid adding the root group.
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      # Forbid adding the root group.
      - min: 1
        max: 65535
  readOnlyRootFilesystem: false
{{- end }}
{{- end }}
