{{- if .Values.server.podDisruptionBudget.enabled }}
apiVersion: {{ template "prometheus.podDisruptionBudget.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ template "prometheus.server.fullname" . }}
  namespace: {{ include "prometheus.namespace" . }}
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
spec:
  maxUnavailable: {{ .Values.server.podDisruptionBudget.maxUnavailable }}
  selector:
    matchLabels:
      {{- include "prometheus.server.matchLabels" . | nindent 6 }}
{{- end }}
