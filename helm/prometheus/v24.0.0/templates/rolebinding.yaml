{{- if and .Values.rbac.create .Values.server.useExistingClusterRoleName .Values.server.namespaces -}}
{{ range $.Values.server.namespaces -}}
---
apiVersion: {{ template "rbac.apiVersion" $ }}
kind: RoleBinding
metadata:
  labels:
    {{- include "prometheus.server.labels" $ | nindent 4 }}
  name: {{ template "prometheus.server.fullname" $ }}
  namespace: {{ . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "prometheus.serviceAccountName.server" $ }}
    namespace: {{ include "prometheus.namespace" $ }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ $.Values.server.useExistingClusterRoleName }}
{{ end -}}
{{ end -}}
