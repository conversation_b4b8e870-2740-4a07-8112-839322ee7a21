{{- if (empty .Values.server.configMapOverrideName) -}}
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
    {{- with .Values.server.extraConfigmapLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ template "prometheus.server.fullname" . }}
  namespace: {{ include "prometheus.namespace" . }}
data:
  allow-snippet-annotations: "false"
{{- $root := . -}}
{{- range $key, $value := .Values.ruleFiles }}
  {{ $key }}: {{- toYaml $value | indent 2 }}
{{- end }}
{{- range $key, $value := .Values.serverFiles }}
  {{ $key }}: |
{{- if eq $key "prometheus.yml" }}
    global:
{{ $root.Values.server.global | toYaml | trimSuffix "\n" | indent 6 }}
{{- if $root.Values.server.remoteWrite }}
    remote_write:
{{ $root.Values.server.remoteWrite | toYaml | indent 4 }}
{{- end }}
{{- if $root.Values.server.remoteRead }}
    remote_read:
{{ $root.Values.server.remoteRead | toYaml | indent 4 }}
{{- end }}
{{- if or $root.Values.server.tsdb $root.Values.server.exemplars }}
    storage:
{{- if $root.Values.server.tsdb }}
      tsdb:
{{ $root.Values.server.tsdb | toYaml | indent 8 }}
{{- end }}
{{- if $root.Values.server.exemplars }}
      exemplars:
{{ $root.Values.server.exemplars | toYaml | indent 8 }}
{{- end }}
{{- end }}
{{- end }}
{{- if eq $key "alerts" }}
{{- if and (not (empty $value)) (empty $value.groups) }}
    groups:
{{- range $ruleKey, $ruleValue := $value }}
    - name: {{ $ruleKey -}}.rules
      rules:
{{ $ruleValue | toYaml | trimSuffix "\n" | indent 6 }}
{{- end }}
{{- else }}
{{ toYaml $value | indent 4 }}
{{- end }}
{{- else }}
{{ toYaml $value | default "{}" | indent 4 }}
{{- end }}
{{- if eq $key "prometheus.yml" -}}
{{- if $root.Values.extraScrapeConfigs }}
{{ tpl $root.Values.extraScrapeConfigs $root | indent 4 }}
{{- end -}}
{{- if or ($root.Values.alertmanager.enabled) ($root.Values.server.alertmanagers) }}
    alerting:
{{- if $root.Values.alertRelabelConfigs }}
{{ $root.Values.alertRelabelConfigs | toYaml  | trimSuffix "\n" | indent 6 }}
{{- end }}
      alertmanagers:
{{- if $root.Values.server.alertmanagers }}
{{ toYaml $root.Values.server.alertmanagers | indent 8 }}
{{- else }}
      - kubernetes_sd_configs:
          - role: pod
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        {{- if $root.Values.alertmanager.prefixURL }}
        path_prefix: {{ $root.Values.alertmanager.prefixURL }}
        {{- end }}
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace]
          regex: {{ $root.Release.Namespace }}
          action: keep
        - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_instance]
          regex: {{ $root.Release.Name }}
          action: keep
        - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
          regex: {{ default "alertmanager" $root.Values.alertmanager.nameOverride | trunc 63 | trimSuffix "-" }}
          action: keep
        - source_labels: [__meta_kubernetes_pod_container_port_number]
          regex: "9093"
          action: keep
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
