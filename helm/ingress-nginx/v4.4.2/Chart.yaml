apiVersion: v2
name: ingress-nginx
# When the version is modified, make sure the artifacthub.io/changes list is updated
# Also update CHANGELOG.md
version: 4.4.2
appVersion: 1.5.1
home: https://github.com/kubernetes/ingress-nginx
description: Ingress controller for Kubernetes using NGINX as a reverse proxy and load balancer
icon: https://upload.wikimedia.org/wikipedia/commons/thumb/c/c5/Nginx_logo.svg/500px-Nginx_logo.svg.png
keywords:
  - ingress
  - nginx
sources:
  - https://github.com/kubernetes/ingress-nginx
maintainers:
  - name: rikatz
  - name: strongjz
  - name: tao12345666333
engine: gotpl
kubeVersion: ">=1.20.0-0"
annotations:
  # Use this annotation to indicate that this chart version is a pre-release.
  # https://artifacthub.io/docs/topics/annotations/helm/
  artifacthub.io/prerelease: "false"
  # List of changes for the release in artifacthub.io
  # https://artifacthub.io/packages/helm/ingress-nginx/ingress-nginx?modal=changelog
  artifacthub.io/changes: |
    - Adding support for disabling liveness and readiness probes to the Helm chart
    - add:(admission-webhooks) ability to set securityContext
    - Updated Helm chart to use the fullname for the electionID if not specified
    - Rename controller-wehbooks-networkpolicy.yaml
