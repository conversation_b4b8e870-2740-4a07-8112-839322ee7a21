{{- if and .Values.defaultBackend.enabled  .Values.defaultBackend.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: default-backend
    {{- with .Values.defaultBackend.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ template "ingress-nginx.defaultBackend.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
automountServiceAccountToken: {{ .Values.defaultBackend.serviceAccount.automountServiceAccountToken }}
{{- end }}
