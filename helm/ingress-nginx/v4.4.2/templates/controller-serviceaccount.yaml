{{- if or .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ template "ingress-nginx.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  {{- if .Values.serviceAccount.annotations }}
  annotations:
  {{- toYaml .Values.serviceAccount.annotations | nindent 4 }}
  {{- end }}
automountServiceAccountToken: {{ .Values.serviceAccount.automountServiceAccountToken }}
{{- end }}
