{{- if and .Values.controller.service.enabled .Values.controller.service.internal.enabled .Values.controller.service.internal.annotations}}
apiVersion: v1
kind: Service
metadata:
  annotations:
  {{- range $key, $value := .Values.controller.service.internal.annotations }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
  {{- if .Values.controller.service.labels }}
    {{- toYaml .Values.controller.service.labels | nindent 4 }}
  {{- end }}
  name: {{ include "ingress-nginx.controller.fullname" . }}-internal
  namespace: {{ .Release.Namespace }}
spec:
  type: "{{ .Values.controller.service.type }}"
{{- if .Values.controller.service.internal.loadBalancerIP }}
  loadBalancerIP: {{ .Values.controller.service.internal.loadBalancerIP }}
{{- end }}
{{- if .Values.controller.service.internal.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{ toYaml .Values.controller.service.internal.loadBalancerSourceRanges | nindent 4 }}
{{- end }}
{{- if .Values.controller.service.internal.externalTrafficPolicy }}
  externalTrafficPolicy: {{ .Values.controller.service.internal.externalTrafficPolicy }}
{{- end }}
  ports:
  {{- $setNodePorts := (or (eq .Values.controller.service.type "NodePort") (eq .Values.controller.service.type "LoadBalancer")) }}
  {{- if .Values.controller.service.enableHttp }}
    - name: http
      port: {{ .Values.controller.service.ports.http }}
      protocol: TCP
      targetPort: {{ .Values.controller.service.targetPorts.http }}
    {{- if semverCompare ">=1.20" .Capabilities.KubeVersion.Version }}
      appProtocol: http
    {{- end }}
    {{- if (and $setNodePorts (not (empty .Values.controller.service.nodePorts.http))) }}
      nodePort: {{ .Values.controller.service.nodePorts.http }}
    {{- end }}
  {{- end }}
  {{- if .Values.controller.service.enableHttps }}
    - name: https
      port: {{ .Values.controller.service.ports.https }}
      protocol: TCP
      targetPort: {{ .Values.controller.service.targetPorts.https }}
    {{- if semverCompare ">=1.20" .Capabilities.KubeVersion.Version }}
      appProtocol: https
    {{- end }}
    {{- if (and $setNodePorts (not (empty .Values.controller.service.nodePorts.https))) }}
      nodePort: {{ .Values.controller.service.nodePorts.https }}
    {{- end }}
  {{- end }}
  {{- range $key, $value := .Values.tcp }}
    - name: {{ if $.Values.portNamePrefix }}{{ $.Values.portNamePrefix }}-{{ end }}{{ $key }}-tcp
      port: {{ $key }}
      protocol: TCP
      targetPort: {{ if $.Values.portNamePrefix }}{{ $.Values.portNamePrefix }}-{{ end }}{{ $key }}-tcp
    {{- if $.Values.controller.service.nodePorts.tcp }}
    {{- if index $.Values.controller.service.nodePorts.tcp $key }}
      nodePort: {{ index $.Values.controller.service.nodePorts.tcp $key }}
    {{- end }}
    {{- end }}
  {{- end }}
  {{- range $key, $value := .Values.udp }}
    - name: {{ if $.Values.portNamePrefix }}{{ $.Values.portNamePrefix }}-{{ end }}{{ $key }}-udp
      port: {{ $key }}
      protocol: UDP
      targetPort: {{ if $.Values.portNamePrefix }}{{ $.Values.portNamePrefix }}-{{ end }}{{ $key }}-udp
    {{- if $.Values.controller.service.nodePorts.udp }}
    {{- if index $.Values.controller.service.nodePorts.udp $key }}
      nodePort: {{ index $.Values.controller.service.nodePorts.udp $key }}
    {{- end }}
    {{- end }}
  {{- end }}
  selector:
    {{- include "ingress-nginx.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: controller
{{- end }}
