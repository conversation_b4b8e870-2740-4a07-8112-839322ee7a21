{{- if .Values.controller.admissionWebhooks.enabled -}}
# before changing this value, check the required kubernetes version
# https://kubernetes.io/docs/reference/access-authn-authz/extensible-admission-controllers/#prerequisites
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  annotations:
  {{- if .Values.controller.admissionWebhooks.certManager.enabled }}
    certmanager.k8s.io/inject-ca-from: {{ printf "%s/%s-admission" .Release.Namespace (include "ingress-nginx.fullname" .) | quote }}
    cert-manager.io/inject-ca-from: {{ printf "%s/%s-admission" .Release.Namespace (include "ingress-nginx.fullname" .) | quote }}
  {{- end }}
  {{- if .Values.controller.admissionWebhooks.annotations }}
    {{- toYaml .Values.controller.admissionWebhooks.annotations | nindent 4 }}
  {{- end }}
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: admission-webhook
    {{- with .Values.controller.admissionWebhooks.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "ingress-nginx.fullname" . }}-admission
webhooks:
  - name: validate.nginx.ingress.kubernetes.io
    matchPolicy: Equivalent
    rules:
      - apiGroups:
          - networking.k8s.io
        apiVersions:
          - v1
        operations:
          - CREATE
          - UPDATE
        resources:
          - ingresses
    failurePolicy: {{ .Values.controller.admissionWebhooks.failurePolicy | default "Fail" }}
    sideEffects: None
    admissionReviewVersions:
      - v1
    clientConfig:
      service:
        namespace: {{ .Release.Namespace | quote }}
        name: {{ include "ingress-nginx.controller.fullname" . }}-admission
        path: /networking/v1/ingresses
    {{- if .Values.controller.admissionWebhooks.timeoutSeconds }}
    timeoutSeconds: {{ .Values.controller.admissionWebhooks.timeoutSeconds }}
    {{- end }}
    {{- if .Values.controller.admissionWebhooks.namespaceSelector }}
    namespaceSelector: {{ toYaml .Values.controller.admissionWebhooks.namespaceSelector | nindent 6 }}
    {{- end }}
    {{- if .Values.controller.admissionWebhooks.objectSelector }}
    objectSelector: {{ toYaml .Values.controller.admissionWebhooks.objectSelector | nindent 6 }}
    {{- end }}
{{- end }}
