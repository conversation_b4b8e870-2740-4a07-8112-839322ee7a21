{{- if and .Values.controller.admissionWebhooks.enabled .Values.controller.admissionWebhooks.patch.enabled (not .Values.controller.admissionWebhooks.certManager.enabled) -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "ingress-nginx.fullname" . }}-admission-patch
  namespace: {{ .Release.Namespace }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
    {{- with .Values.controller.admissionWebhooks.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: admission-webhook
    {{- with .Values.controller.admissionWebhooks.patch.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
{{- if .Capabilities.APIVersions.Has "batch/v1alpha1" }}
  # Alpha feature since k8s 1.12
  ttlSecondsAfterFinished: 0
{{- end }}
  template:
    metadata:
      name: {{ include "ingress-nginx.fullname" . }}-admission-patch
    {{- if .Values.controller.admissionWebhooks.patch.podAnnotations }}
      annotations: {{ toYaml .Values.controller.admissionWebhooks.patch.podAnnotations | nindent 8 }}
    {{- end }}
      labels:
        {{- include "ingress-nginx.labels" . | nindent 8 }}
        app.kubernetes.io/component: admission-webhook
        {{- with .Values.controller.admissionWebhooks.patch.labels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
    {{- if .Values.controller.admissionWebhooks.patch.priorityClassName }}
      priorityClassName: {{ .Values.controller.admissionWebhooks.patch.priorityClassName }}
    {{- end }}
    {{- if .Values.imagePullSecrets }}
      imagePullSecrets: {{ toYaml .Values.imagePullSecrets | nindent 8 }}
    {{- end }}
      containers:
        - name: patch
          {{- with .Values.controller.admissionWebhooks.patch.image }}
          image: "{{- if .repository -}}{{ .repository }}{{ else }}{{ .registry }}/{{ .image }}{{- end -}}:{{ .tag }}{{- if (.digest) -}} @{{.digest}} {{- end -}}"
          {{- end }}
          imagePullPolicy: {{ .Values.controller.admissionWebhooks.patch.image.pullPolicy }}
          args:
            - patch
            - --webhook-name={{ include "ingress-nginx.fullname" . }}-admission
            - --namespace=$(POD_NAMESPACE)
            - --patch-mutating=false
            - --secret-name={{ include "ingress-nginx.fullname" . }}-admission
            - --patch-failure-policy={{ .Values.controller.admissionWebhooks.failurePolicy }}
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          {{- if .Values.controller.admissionWebhooks.extraEnvs }}
            {{- toYaml .Values.controller.admissionWebhooks.extraEnvs | nindent 12 }}
          {{- end }}
          {{- if .Values.controller.admissionWebhooks.patchWebhookJob.securityContext }}
          securityContext: {{ toYaml .Values.controller.admissionWebhooks.patchWebhookJob.securityContext | nindent 12 }}
          {{- end }}
          {{- if .Values.controller.admissionWebhooks.patchWebhookJob.resources }}
          resources: {{ toYaml .Values.controller.admissionWebhooks.patchWebhookJob.resources | nindent 12 }}
          {{- end }}
      restartPolicy: OnFailure
      serviceAccountName: {{ include "ingress-nginx.fullname" . }}-admission
    {{- if .Values.controller.admissionWebhooks.patch.nodeSelector }}
      nodeSelector: {{ toYaml .Values.controller.admissionWebhooks.patch.nodeSelector | nindent 8 }}
    {{- end }}
    {{- if .Values.controller.admissionWebhooks.patch.tolerations }}
      tolerations: {{ toYaml .Values.controller.admissionWebhooks.patch.tolerations | nindent 8 }}
    {{- end }}
      {{- if .Values.controller.admissionWebhooks.patch.securityContext }}
      securityContext:
        {{- toYaml .Values.controller.admissionWebhooks.patch.securityContext | nindent 8 }}
      {{- end }}
{{- end }}
