{{- if .Values.defaultBackend.enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: default-backend
    {{- with .Values.defaultBackend.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "ingress-nginx.defaultBackend.fullname" . }}
  namespace: {{ include "ingress-nginx.namespace" . }}
spec:
  selector:
    matchLabels:
      {{- include "ingress-nginx.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: default-backend
{{- if not .Values.defaultBackend.autoscaling.enabled }}
  replicas: {{ .Values.defaultBackend.replicaCount }}
{{- end }}
  revisionHistoryLimit: {{ .Values.revisionHistoryLimit }}
  {{- if .Values.defaultBackend.updateStrategy }}
  strategy:
    {{ toYaml .Values.defaultBackend.updateStrategy | nindent 4 }}
  {{- end }}
  minReadySeconds: {{ .Values.defaultBackend.minReadySeconds }}
  template:
    metadata:
    {{- if .Values.defaultBackend.podAnnotations }}
      annotations: {{ toYaml .Values.defaultBackend.podAnnotations | nindent 8 }}
    {{- end }}
      labels:
        {{- include "ingress-nginx.labels" . | nindent 8 }}
        app.kubernetes.io/component: default-backend
        {{- with .Values.defaultBackend.labels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- if .Values.defaultBackend.podLabels }}
        {{- toYaml .Values.defaultBackend.podLabels | nindent 8 }}
      {{- end }}
    spec:
    {{- if .Values.imagePullSecrets }}
      imagePullSecrets: {{ toYaml .Values.imagePullSecrets | nindent 8 }}
    {{- end }}
    {{- if .Values.defaultBackend.priorityClassName }}
      priorityClassName: {{ .Values.defaultBackend.priorityClassName }}
    {{- end }}
    {{- if .Values.defaultBackend.podSecurityContext }}
      securityContext: {{ toYaml .Values.defaultBackend.podSecurityContext | nindent 8 }}
    {{- end }}
      containers:
        - name: {{ template "ingress-nginx.name" . }}-default-backend
          {{- with .Values.defaultBackend.image }}
          image: {{ if .repository }}{{ .repository }}{{ else }}{{ .registry }}/{{ .image }}{{ end }}:{{ .tag }}{{ if .digest }}@{{ .digest }}{{ end }}
          {{- end }}
          imagePullPolicy: {{ .Values.defaultBackend.image.pullPolicy }}
        {{- if .Values.defaultBackend.extraArgs }}
          args:
          {{- range $key, $value := .Values.defaultBackend.extraArgs }}
            {{- /* Accept keys without values or with false as value */}}
            {{- if eq ($value | quote | len) 2 }}
            - --{{ $key }}
            {{- else }}
            - --{{ $key }}={{ $value }}
            {{- end }}
          {{- end }}
        {{- end }}
          securityContext: {{ include "ingress-nginx.defaultBackend.containerSecurityContext" . | nindent 12 }}
        {{- if .Values.defaultBackend.extraEnvs }}
          env: {{ toYaml .Values.defaultBackend.extraEnvs | nindent 12 }}
        {{- end }}
          livenessProbe:
            httpGet:
              path: /healthz
              port: {{ .Values.defaultBackend.port }}
              scheme: HTTP
            initialDelaySeconds: {{ .Values.defaultBackend.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.defaultBackend.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.defaultBackend.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.defaultBackend.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.defaultBackend.livenessProbe.failureThreshold }}
          readinessProbe:
            httpGet:
              path: /healthz
              port: {{ .Values.defaultBackend.port }}
              scheme: HTTP
            initialDelaySeconds: {{ .Values.defaultBackend.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.defaultBackend.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.defaultBackend.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.defaultBackend.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.defaultBackend.readinessProbe.failureThreshold }}
          ports:
            - name: http
              containerPort: {{ .Values.defaultBackend.port }}
              protocol: TCP
        {{- if .Values.defaultBackend.extraVolumeMounts }}
          volumeMounts: {{- toYaml .Values.defaultBackend.extraVolumeMounts | nindent 12 }}
        {{- end }}
        {{- if .Values.defaultBackend.resources }}
          resources: {{ toYaml .Values.defaultBackend.resources | nindent 12 }}
        {{- end }}
    {{- if .Values.defaultBackend.nodeSelector }}
      nodeSelector: {{ toYaml .Values.defaultBackend.nodeSelector | nindent 8 }}
    {{- end }}
      serviceAccountName: {{ template "ingress-nginx.defaultBackend.serviceAccountName" . }}
    {{- if .Values.defaultBackend.tolerations }}
      tolerations: {{ toYaml .Values.defaultBackend.tolerations | nindent 8 }}
    {{- end }}
    {{- if .Values.defaultBackend.affinity }}
      affinity: {{ toYaml .Values.defaultBackend.affinity | nindent 8 }}
    {{- end }}
      terminationGracePeriodSeconds: 60
    {{- if .Values.defaultBackend.extraVolumes }}
      volumes: {{ toYaml .Values.defaultBackend.extraVolumes | nindent 8 }}
    {{- end }}
{{- end }}
