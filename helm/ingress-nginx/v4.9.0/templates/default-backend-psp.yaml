{{- if (semverCompare "<1.25.0-0" .Capabilities.KubeVersion.Version) }}
{{- if and .Values.podSecurityPolicy.enabled .Values.defaultBackend.enabled (empty .Values.defaultBackend.existingPsp) -}}
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: {{ include "ingress-nginx.fullname" . }}-backend
  annotations:
    seccomp.security.alpha.kubernetes.io/allowedProfileNames: "*"
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: default-backend
    {{- with .Values.defaultBackend.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  privileged: false
  hostPID: false
  hostIPC: false
  hostNetwork: false
  volumes:
    - configMap
    - downwardAPI
    - emptyDir
    - secret
    - projected
  fsGroup:
    rule: MustRunAs
    ranges:
      - min: 1
        max: 65535
  readOnlyRootFilesystem: true
  runAsUser:
    rule: MustRunAsNonRoot
  runAsGroup:
    rule: MustRunAs
    ranges:
      - min: 1
        max: 65535
  supplementalGroups:
    rule: MustRunAs
    ranges:
      - min: 1
        max: 65535
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  seLinux:
    rule: RunAsAny
{{- end }}
{{- end }}
