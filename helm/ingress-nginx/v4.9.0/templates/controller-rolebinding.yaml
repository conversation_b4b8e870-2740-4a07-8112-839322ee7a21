{{- if .Values.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "ingress-nginx.fullname" . }}
  namespace: {{ include "ingress-nginx.namespace" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "ingress-nginx.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "ingress-nginx.serviceAccountName" . }}
    namespace: {{ include "ingress-nginx.namespace" . }}
{{- end }}
