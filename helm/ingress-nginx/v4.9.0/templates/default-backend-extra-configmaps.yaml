{{- if .Values.defaultBackend.enabled }}
{{- range .Values.defaultBackend.extraConfigMaps }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "ingress-nginx.labels" $ | nindent 4 }}
    app.kubernetes.io/component: default-backend
    {{- with $.Values.defaultBackend.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ .name }}
  namespace: {{ include "ingress-nginx.namespace" $ }}
data:
  {{- with .data }}
  {{- toYaml . | nindent 2 }}
  {{- end }}
{{- end }}
{{- end }}
