{{- if .Values.controller.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "ingress-nginx.controller.fullname" . }}
  namespace: {{ include "ingress-nginx.namespace" . }}
spec:
  podSelector:
    matchLabels:
      {{- include "ingress-nginx.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: controller
  policyTypes:
    - Ingress
    - Egress
  ingress:
    - ports:
      {{- range $key, $value := .Values.controller.containerPort }}
        - protocol: TCP
          port: {{ $value }}
      {{- end }}
      {{- if .Values.controller.metrics.enabled }}
        - protocol: TCP
          port: {{ .Values.controller.metrics.port }}
      {{- end }}
      {{- if .Values.controller.admissionWebhooks.enabled }}
        - protocol: TCP
          port: {{ .Values.controller.admissionWebhooks.port }}
      {{- end }}
      {{- range $key, $value := .Values.tcp }}
        - protocol: TCP
          port: {{ $key }}
      {{- end }}
      {{- range $key, $value := .Values.udp }}
        - protocol: UDP
          port: {{ $key }}
      {{- end }}
  egress:
    - {}
{{- end }}
