{{- if .Values.udp -}}
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- if .Values.controller.udp.annotations }}
  annotations: {{ toYaml .Values.controller.udp.annotations | nindent 4 }}
{{- end }}
  name: {{ include "ingress-nginx.fullname" . }}-udp
  namespace: {{ include "ingress-nginx.namespace" . }}
data: {{ tpl (toYaml .Values.udp) . | nindent 2 }}
{{- end }}
