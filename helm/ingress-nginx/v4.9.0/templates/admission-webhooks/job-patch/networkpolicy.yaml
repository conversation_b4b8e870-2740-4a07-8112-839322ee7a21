{{- if and .Values.controller.admissionWebhooks.enabled .Values.controller.admissionWebhooks.patch.enabled .Values.controller.admissionWebhooks.patch.networkPolicy.enabled (not .Values.controller.admissionWebhooks.certManager.enabled) -}}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "ingress-nginx.admissionWebhooks.fullname" . }}
  namespace: {{ include "ingress-nginx.namespace" . }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade,post-install,post-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: admission-webhook
    {{- with .Values.controller.admissionWebhooks.patch.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  podSelector:
    matchLabels:
      {{- include "ingress-nginx.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: admission-webhook
  policyTypes:
    - Ingress
    - Egress
  egress:
    - {}
{{- end }}
