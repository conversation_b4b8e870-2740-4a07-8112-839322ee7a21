{{- if (semverCompare "<1.25.0-0" .Capabilities.KubeVersion.Version) }}
{{- if and .Values.podSecurityPolicy.enabled (empty .Values.controller.existingPsp) -}}
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: {{ include "ingress-nginx.fullname" . }}
  annotations:
    seccomp.security.alpha.kubernetes.io/allowedProfileNames: "*"
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  privileged: false
  hostPID: false
  hostIPC: false
  hostNetwork: {{ .Values.controller.hostNetwork }}
{{- if or .Values.controller.hostNetwork .Values.controller.hostPort.enabled }}
  hostPorts:
  {{- if .Values.controller.hostNetwork }}
  {{- range $key, $value := .Values.controller.containerPort }}
    # controller.containerPort.{{ $key }}
    - min: {{ $value }}
      max: {{ $value }}
  {{- end }}
  {{- else if .Values.controller.hostPort.enabled }}
  {{- range $key, $value := .Values.controller.hostPort.ports }}
    # controller.hostPort.ports.{{ $key }}
    - min: {{ $value }}
      max: {{ $value }}
  {{- end }}
  {{- end }}
  {{- if .Values.controller.metrics.enabled }}
    # controller.metrics.port
    - min: {{ .Values.controller.metrics.port }}
      max: {{ .Values.controller.metrics.port }}
  {{- end }}
  {{- if .Values.controller.admissionWebhooks.enabled }}
    # controller.admissionWebhooks.port
    - min: {{ .Values.controller.admissionWebhooks.port }}
      max: {{ .Values.controller.admissionWebhooks.port }}
  {{- end }}
  {{- range $key, $value := .Values.tcp }}
    # tcp.{{ $key }}
    - min: {{ $key }}
      max: {{ $key }}
  {{- end }}
  {{- range $key, $value := .Values.udp }}
    # udp.{{ $key }}
    - min: {{ $key }}
      max: {{ $key }}
  {{- end }}
{{- end }}
  volumes:
    - configMap
    - downwardAPI
    - emptyDir
    - secret
    - projected
  fsGroup:
    rule: MustRunAs
    ranges:
      - min: 1
        max: 65535
  readOnlyRootFilesystem: false
  runAsUser:
    rule: MustRunAsNonRoot
  runAsGroup:
    rule: MustRunAs
    ranges:
      - min: 1
        max: 65535
  supplementalGroups:
    rule: MustRunAs
    ranges:
      - min: 1
        max: 65535
  allowPrivilegeEscalation: {{ or .Values.controller.image.allowPrivilegeEscalation .Values.controller.image.chroot }}
  requiredDropCapabilities:
    - ALL
  allowedCapabilities:
    - NET_BIND_SERVICE
  {{- if .Values.controller.image.chroot }}
  {{- if .Values.controller.image.seccompProfile }}
    - SYS_ADMIN
  {{- end }}
    - SYS_CHROOT
  {{- end }}
  seLinux:
    rule: RunAsAny
{{- if .Values.controller.sysctls }}
  allowedUnsafeSysctls:
  {{- range $sysctl, $value := .Values.controller.sysctls }}
    - {{ $sysctl }}
  {{- end }}
{{- end }}
{{- end }}
{{- end }}
