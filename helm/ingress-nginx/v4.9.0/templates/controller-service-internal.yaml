{{- if and .Values.controller.service.enabled .Values.controller.service.internal.enabled .Values.controller.service.internal.annotations -}}
apiVersion: v1
kind: Service
metadata:
  annotations:
  {{- range $key, $value := .Values.controller.service.internal.annotations }}
    {{ $key }}: {{ tpl ($value | toString) $ | quote }}
  {{- end }}
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
  {{- if .Values.controller.service.labels }}
    {{- toYaml .Values.controller.service.labels | nindent 4 }}
  {{- end }}
  name: {{ include "ingress-nginx.controller.fullname" . }}-internal
  namespace: {{ include "ingress-nginx.namespace" . }}
spec:
  type: {{ .Values.controller.service.internal.type | default .Values.controller.service.type }}
{{- if .Values.controller.service.internal.clusterIP }}
  clusterIP: {{ .Values.controller.service.internal.clusterIP }}
{{- end }}
{{- if .Values.controller.service.internal.externalIPs }}
  externalIPs: {{ toYaml .Values.controller.service.internal.externalIPs | nindent 4 }}
{{- end }}
{{- if .Values.controller.service.internal.loadBalancerIP }}
  loadBalancerIP: {{ .Values.controller.service.internal.loadBalancerIP }}
{{- end }}
{{- if .Values.controller.service.internal.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{ toYaml .Values.controller.service.internal.loadBalancerSourceRanges | nindent 4 }}
{{- end }}
{{- if .Values.controller.service.internal.loadBalancerClass }}
  loadBalancerClass: {{ .Values.controller.service.internal.loadBalancerClass }}
{{- end }}
{{- if hasKey .Values.controller.service.internal "allocateLoadBalancerNodePorts" }}
  allocateLoadBalancerNodePorts: {{ .Values.controller.service.internal.allocateLoadBalancerNodePorts }}
{{- end }}
{{- if .Values.controller.service.internal.externalTrafficPolicy }}
  externalTrafficPolicy: {{ .Values.controller.service.internal.externalTrafficPolicy }}
{{- end }}
{{- if .Values.controller.service.internal.sessionAffinity }}
  sessionAffinity: {{ .Values.controller.service.internal.sessionAffinity }}
{{- end }}
{{- if .Values.controller.service.internal.healthCheckNodePort }}
  healthCheckNodePort: {{ .Values.controller.service.internal.healthCheckNodePort }}
{{- end }}
{{- if semverCompare ">=1.21.0-0" .Capabilities.KubeVersion.Version -}}
{{- if .Values.controller.service.internal.ipFamilyPolicy }}
  ipFamilyPolicy: {{ .Values.controller.service.internal.ipFamilyPolicy }}
{{- end }}
{{- if .Values.controller.service.internal.ipFamilies }}
  ipFamilies: {{ toYaml .Values.controller.service.internal.ipFamilies | nindent 4 }}
{{- end }}
{{- end }}
  ports:
  {{- $setNodePorts := (or (eq .Values.controller.service.type "NodePort") (eq .Values.controller.service.type "LoadBalancer")) }}
  {{- if .Values.controller.service.enableHttp }}
    - name: http
      port: {{ .Values.controller.service.internal.ports.http | default .Values.controller.service.ports.http }}
      protocol: TCP
      targetPort: {{ .Values.controller.service.internal.targetPorts.http | default .Values.controller.service.targetPorts.http }}
    {{- if and (semverCompare ">=1.20" .Capabilities.KubeVersion.Version) (.Values.controller.service.internal.appProtocol) }}
      appProtocol: http
    {{- end }}
    {{- if (and $setNodePorts (not (empty .Values.controller.service.internal.nodePorts.http))) }}
      nodePort: {{ .Values.controller.service.internal.nodePorts.http }}
    {{- end }}
  {{- end }}
  {{- if .Values.controller.service.enableHttps }}
    - name: https
      port: {{ .Values.controller.service.internal.ports.https | default .Values.controller.service.ports.https }}
      protocol: TCP
      targetPort: {{ .Values.controller.service.internal.targetPorts.https | default .Values.controller.service.targetPorts.https }}
    {{- if and (semverCompare ">=1.20" .Capabilities.KubeVersion.Version) (.Values.controller.service.internal.appProtocol) }}
      appProtocol: https
    {{- end }}
    {{- if (and $setNodePorts (not (empty .Values.controller.service.internal.nodePorts.https))) }}
      nodePort: {{ .Values.controller.service.internal.nodePorts.https }}
    {{- end }}
  {{- end }}
  {{- range $key, $value := .Values.tcp }}
    - name: {{ if $.Values.portNamePrefix }}{{ $.Values.portNamePrefix }}-{{ end }}{{ $key }}-tcp
      port: {{ $key }}
      protocol: TCP
      targetPort: {{ if $.Values.portNamePrefix }}{{ $.Values.portNamePrefix }}-{{ end }}{{ $key }}-tcp
    {{- if $.Values.controller.service.internal.nodePorts.tcp }}
    {{- if index $.Values.controller.service.internal.nodePorts.tcp $key }}
      nodePort: {{ index $.Values.controller.service.internal.nodePorts.tcp $key }}
    {{- end }}
    {{- end }}
  {{- end }}
  {{- range $key, $value := .Values.udp }}
    - name: {{ if $.Values.portNamePrefix }}{{ $.Values.portNamePrefix }}-{{ end }}{{ $key }}-udp
      port: {{ $key }}
      protocol: UDP
      targetPort: {{ if $.Values.portNamePrefix }}{{ $.Values.portNamePrefix }}-{{ end }}{{ $key }}-udp
    {{- if $.Values.controller.service.internal.nodePorts.udp }}
    {{- if index $.Values.controller.service.internal.nodePorts.udp $key }}
      nodePort: {{ index $.Values.controller.service.internal.nodePorts.udp $key }}
    {{- end }}
    {{- end }}
  {{- end }}
  selector:
    {{- include "ingress-nginx.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: controller
{{- end }}
