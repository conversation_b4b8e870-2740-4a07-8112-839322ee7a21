annotations:
  artifacthub.io/changes: |-
    - "Add controller.metrics.serviceMonitor.annotations in Helm chart"
    - "fix(labels): use complete labels variable on default-backend deployment"
    - "chart: allow setting allocateLoadBalancerNodePorts (#10693)"
    - "[release-1.9] feat(helm): add documentation about metric args (#10695)"
    - "Update Ingress-Nginx version controller-v1.9.5"
  artifacthub.io/prerelease: "false"
apiVersion: v2
appVersion: 1.9.5
description: Ingress controller for Kubernetes using NGINX as a reverse proxy and
  load balancer
home: https://github.com/kubernetes/ingress-nginx
icon: https://upload.wikimedia.org/wikipedia/commons/thumb/c/c5/Nginx_logo.svg/500px-Nginx_logo.svg.png
keywords:
- ingress
- nginx
kubeVersion: '>=1.20.0-0'
maintainers:
- name: rikatz
- name: strongjz
- name: tao12345666333
name: ingress-nginx
sources:
- https://github.com/kubernetes/ingress-nginx
version: 4.9.0
