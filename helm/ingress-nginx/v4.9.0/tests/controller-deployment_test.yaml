suite: Controller > Deployment
templates:
  - controller-deployment.yaml

tests:
  - it: should create a Deployment
    asserts:
      - hasDocuments:
          count: 1
      - isKind:
          of: Deployment
      - equal:
          path: metadata.name
          value: RELEASE-NAME-ingress-nginx-controller

  - it: should create a Deployment with 3 replicas if `controller.replicaCount` is 3
    set:
      controller.replicaCount: 3
    asserts:
      - equal:
          path: spec.replicas
          value: 3

  - it: should create a Deployment with resource limits if `controller.resources.limits` is set
    set:
      controller.resources.limits.cpu: 500m
      controller.resources.limits.memory: 512Mi
    asserts:
      - equal:
          path: spec.template.spec.containers[0].resources.limits.cpu
          value: 500m
      - equal:
          path: spec.template.spec.containers[0].resources.limits.memory
          value: 512Mi
