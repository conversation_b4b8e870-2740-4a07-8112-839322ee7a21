suite: Controller > ConfigMap > Add Headers
templates:
  - controller-configmap-addheaders.yaml

tests:
  - it: should not create a ConfigMap if `controller.addHeaders` is not set
    set:
      controller.addHeaders: null
    asserts:
      - hasDocuments:
          count: 0

  - it: should create a ConfigMap if `controller.addHeaders` is set
    set:
      controller.addHeaders:
        X-Another-Custom-Header: Value
    asserts:
      - hasDocuments:
          count: 1
      - isKind:
          of: ConfigMap
      - equal:
          path: metadata.name
          value: RELEASE-NAME-ingress-nginx-custom-add-headers
      - equal:
          path: data.X-Another-Custom-Header
          value: Value
