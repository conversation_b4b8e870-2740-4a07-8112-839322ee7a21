suite: Controller > Service
templates:
  - controller-service.yaml

tests:
  - it: should not create a Service if `controller.service.external.enabled` is false
    set:
      controller.service.external.enabled: false
    asserts:
      - hasDocuments:
          count: 0

  - it: should create a Service if `controller.service.external.enabled` is true
    set:
      controller.service.external.enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - isKind:
          of: Service
      - equal:
          path: metadata.name
          value: RELEASE-NAME-ingress-nginx-controller

  - it: should create a Service of type "NodePort" if `controller.service.external.enabled` is true and `controller.service.type` is "NodePort"
    set:
      controller.service.external.enabled: true
      controller.service.type: NodePort
    asserts:
      - equal:
          path: spec.type
          value: NodePort
