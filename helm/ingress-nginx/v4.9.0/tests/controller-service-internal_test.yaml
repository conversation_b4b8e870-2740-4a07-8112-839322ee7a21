suite: Controller > Service > Internal
templates:
  - controller-service-internal.yaml

tests:
  - it: should not create an internal Service if `controller.service.internal.enabled` is false
    set:
      controller.service.internal.enabled: false
    asserts:
      - hasDocuments:
          count: 0

  - it: should create an internal Service if `controller.service.internal.enabled` is true and `controller.service.internal.annotations` are set
    set:
      controller.service.internal.enabled: true
      controller.service.internal.annotations:
        test.annotation: "true"
    asserts:
      - hasDocuments:
          count: 1
      - isKind:
          of: Service
      - equal:
          path: metadata.name
          value: RELEASE-NAME-ingress-nginx-controller-internal
