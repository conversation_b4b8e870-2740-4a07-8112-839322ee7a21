suite: Controller > KEDA
templates:
  - controller-keda.yaml

tests:
  - it: should create a ScaledObject if `controller.kind` is "Deployment" and `controller.keda.enabled` is true
    set:
      controller.kind: Deployment
      controller.keda.enabled: true
    asserts:
      - hasDocuments:
          count: 1
      - isKind:
          of: ScaledObject
      - equal:
          path: metadata.name
          value: RELEASE-NAME-ingress-nginx-controller
