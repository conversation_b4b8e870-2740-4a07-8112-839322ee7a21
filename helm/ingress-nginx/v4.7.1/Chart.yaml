annotations:
  artifacthub.io/changes: |
     - "Added a doc line to the missing helm value service.internal.loadBalancerIP (#9406)"
     - "feat(helm): Add loadBalancerClass (#9562)"
     - "added helmshowvalues example (#10019)"
     - "Update Ingress-Nginx version controller-v1.8.1"
  artifacthub.io/prerelease: "false"
apiVersion: v2
appVersion: 1.8.1
description: Ingress controller for Kubernetes using NGINX as a reverse proxy and
  load balancer
engine: gotpl
home: https://github.com/kubernetes/ingress-nginx
icon: https://upload.wikimedia.org/wikipedia/commons/thumb/c/c5/Nginx_logo.svg/500px-Nginx_logo.svg.png
keywords:
- ingress
- nginx
kubeVersion: '>=1.20.0-0'
maintainers:
- name: rikatz
- name: strongjz
- name: tao12345666333
name: ingress-nginx
sources:
- https://github.com/kubernetes/ingress-nginx
version: 4.7.1
