The ingress-nginx controller has been installed.

{{- if contains "NodePort" .Values.controller.service.type }}
Get the application URL by running these commands:

{{- if (not (empty .Values.controller.service.nodePorts.http)) }}
  export HTTP_NODE_PORT={{ .Values.controller.service.nodePorts.http }}
{{- else }}
  export HTTP_NODE_PORT=$(kubectl --namespace {{ .Release.Namespace }} get services -o jsonpath="{.spec.ports[0].nodePort}" {{ include "ingress-nginx.controller.fullname" . }})
{{- end }}
{{- if (not (empty .Values.controller.service.nodePorts.https)) }}
  export HTTPS_NODE_PORT={{ .Values.controller.service.nodePorts.https }}
{{- else }}
  export HTTPS_NODE_PORT=$(kubectl --namespace {{ .Release.Namespace }} get services -o jsonpath="{.spec.ports[1].nodePort}" {{ include "ingress-nginx.controller.fullname" . }})
{{- end }}
  export NODE_IP=$(kubectl --namespace {{ .Release.Namespace }} get nodes -o jsonpath="{.items[0].status.addresses[1].address}")

  echo "Visit http://$NODE_IP:$HTTP_NODE_PORT to access your application via HTTP."
  echo "Visit https://$NODE_IP:$HTTPS_NODE_PORT to access your application via HTTPS."
{{- else if contains "LoadBalancer" .Values.controller.service.type }}
It may take a few minutes for the LoadBalancer IP to be available.
You can watch the status by running 'kubectl --namespace {{ .Release.Namespace }} get services -o wide -w {{ include "ingress-nginx.controller.fullname" . }}'
{{- else if contains "ClusterIP"  .Values.controller.service.type }}
Get the application URL by running these commands:
  export POD_NAME=$(kubectl --namespace {{ .Release.Namespace }} get pods -o jsonpath="{.items[0].metadata.name}" -l "app={{ template "ingress-nginx.name" . }},component={{ .Values.controller.name }},release={{ .Release.Name }}")
  kubectl --namespace {{ .Release.Namespace }} port-forward $POD_NAME 8080:80
  echo "Visit http://127.0.0.1:8080 to access your application."
{{- end }}

An example Ingress that makes use of the controller:

{{- $isV1 := semverCompare ">=1" .Chart.AppVersion}}
  apiVersion: networking.k8s.io/v1
  kind: Ingress
  metadata:
    name: example
    namespace: foo
    {{- if eq $isV1 false }}
    annotations:
      kubernetes.io/ingress.class: {{ .Values.controller.ingressClass }}
    {{- end }}
  spec:
    {{- if $isV1 }}
    ingressClassName: {{ .Values.controller.ingressClassResource.name }}
    {{- end }}
    rules:
      - host: www.example.com
        http:
          paths:
            - pathType: Prefix
              backend:
                service:
                  name: exampleService
                  port:
                    number: 80
              path: /
    # This section is only required if TLS is to be enabled for the Ingress
    tls:
      - hosts:
        - www.example.com
        secretName: example-tls

If TLS is enabled for the Ingress, a Secret containing the certificate and key must also be provided:

  apiVersion: v1
  kind: Secret
  metadata:
    name: example-tls
    namespace: foo
  data:
    tls.crt: <base64 encoded cert>
    tls.key: <base64 encoded key>
  type: kubernetes.io/tls
