{{- if and .Values.controller.keda.enabled (or (eq .Values.controller.kind "Deployment") (eq .Values.controller.kind "Both")) -}}
# https://keda.sh/docs/

apiVersion: {{ .Values.controller.keda.apiVersion }}
kind: ScaledObject
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "ingress-nginx.controller.fullname" . }}
  {{- if .Values.controller.keda.scaledObject.annotations }}
  annotations: {{ toYaml .Values.controller.keda.scaledObject.annotations | nindent 4 }}
  {{- end }}
spec:
  scaleTargetRef:
{{- if eq .Values.controller.keda.apiVersion "keda.k8s.io/v1alpha1" }}
    deploymentName: {{ include "ingress-nginx.controller.fullname" . }}
{{- else if eq .Values.controller.keda.apiVersion "keda.sh/v1alpha1" }}
    name: {{ include "ingress-nginx.controller.fullname" . }}
{{- end }}
  pollingInterval: {{ .Values.controller.keda.pollingInterval }}
  cooldownPeriod: {{ .Values.controller.keda.cooldownPeriod }}
  minReplicaCount: {{ .Values.controller.keda.minReplicas }}
  maxReplicaCount: {{ .Values.controller.keda.maxReplicas }}
{{- with .Values.controller.keda.fallback }}
  fallback:
    failureThreshold: {{ .failureThreshold | default 3 }}
    replicas: {{ .replicas | default $.Values.controller.keda.maxReplicas }}
{{- end }}
  triggers:
{{- with .Values.controller.keda.triggers }}
{{ toYaml . | indent 2 }}
{{ end }}
  advanced:
    restoreToOriginalReplicaCount: {{ .Values.controller.keda.restoreToOriginalReplicaCount }}
{{- if .Values.controller.keda.behavior }}
    horizontalPodAutoscalerConfig:
      behavior:
{{ with .Values.controller.keda.behavior -}}
{{ toYaml . | indent 8 }}
{{ end }}

{{- end }}
{{- end }}
