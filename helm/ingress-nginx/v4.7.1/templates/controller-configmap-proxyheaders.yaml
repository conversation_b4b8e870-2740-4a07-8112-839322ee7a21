{{- if .Values.controller.proxySetHeaders -}}
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "ingress-nginx.fullname" . }}-custom-proxy-headers
  namespace: {{ .Release.Namespace }}
data: {{ toYaml .Values.controller.proxySetHeaders | nindent 2 }}
{{- end }}
