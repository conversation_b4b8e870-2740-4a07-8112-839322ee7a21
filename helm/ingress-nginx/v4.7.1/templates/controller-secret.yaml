{{- if .Values.dhParam -}}
apiVersion: v1
kind: Secret
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "ingress-nginx.controller.fullname" . }}
  namespace: {{ .Release.Namespace }}
data:
  dhparam.pem: {{ .Values.dhParam }}
{{- end }}
