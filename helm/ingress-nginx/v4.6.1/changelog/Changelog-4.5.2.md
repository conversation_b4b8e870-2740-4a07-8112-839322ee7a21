# Changelog

This file documents all notable changes to [ingress-nginx](https://github.com/kubernetes/ingress-nginx) Helm Chart. The release numbering uses [semantic versioning](http://semver.org).

### 4.5.2

* add lint on chart before release (#9570)
* ci: remove setup-helm step (#9404)
* feat(helm): Optionally use cert-manager instead admission patch (#9279)
* run helm release on main only and when the chart/value changes only (#9290)
* Update Ingress-Nginx version controller-v1.6.4

**Full Changelog**: https://github.com/kubernetes/ingress-nginx/compare/helm-chart-4.4.3...helm-chart-4.5.2
