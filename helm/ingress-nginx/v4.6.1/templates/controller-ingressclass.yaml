{{- if .Values.controller.ingressClassResource.enabled -}}
# We don't support namespaced ingressClass yet
# So a ClusterRole and a ClusterRoleBinding is required
apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ .Values.controller.ingressClassResource.name }}
{{- if .Values.controller.ingressClassResource.default }}
  annotations:
    ingressclass.kubernetes.io/is-default-class: "true"
{{- end }}
spec:
  controller: {{ .Values.controller.ingressClassResource.controllerValue }}
  {{ template "ingressClass.parameters" . }}
{{- end }}
