{{- if (semverCompare "<1.25.0-0" .Capabilities.KubeVersion.Version) }}
{{- if and .Values.podSecurityPolicy.enabled (empty .Values.controller.existingPsp) -}}
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: {{ include "ingress-nginx.fullname" . }}
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  allowedCapabilities:
    - NET_BIND_SERVICE
  {{- if .Values.controller.image.chroot }}
    - SYS_CHROOT
  {{- end }}
{{- if .Values.controller.sysctls }}
  allowedUnsafeSysctls:
  {{- range $sysctl, $value := .Values.controller.sysctls }}
  - {{ $sysctl }}
  {{- end }}
{{- end }}
  privileged: false
  allowPrivilegeEscalation: true
  # Allow core volume types.
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
{{- if .Values.controller.hostNetwork }}
  hostNetwork: {{ .Values.controller.hostNetwork }}
{{- end }}
{{- if or .Values.controller.hostNetwork .Values.controller.hostPort.enabled }}
  hostPorts:
{{- if .Values.controller.hostNetwork }}
{{- range $key, $value := .Values.controller.containerPort }}
  # {{ $key }}
  - min: {{ $value }}
    max: {{ $value }}
{{- end }}
{{- else if .Values.controller.hostPort.enabled }}
{{- range $key, $value := .Values.controller.hostPort.ports }}
  # {{ $key }}
  - min: {{ $value }}
    max: {{ $value }}
{{- end }}
{{- end }}
{{- if .Values.controller.metrics.enabled }}
  # metrics
  - min: {{ .Values.controller.metrics.port }}
    max: {{ .Values.controller.metrics.port }}
{{- end }}
{{- if .Values.controller.admissionWebhooks.enabled }}
  # admission webhooks
  - min: {{ .Values.controller.admissionWebhooks.port }}
    max: {{ .Values.controller.admissionWebhooks.port }}
{{- end }}
{{- range $key, $value := .Values.tcp }}
  # {{ $key }}-tcp
  - min: {{ $key }}
    max: {{ $key }}
{{- end }}
{{- range $key, $value := .Values.udp }}
  # {{ $key }}-udp
  - min: {{ $key }}
    max: {{ $key }}
{{- end }}
{{- end }}
  hostIPC: false
  hostPID: false
  runAsUser:
    # Require the container to run without root privileges.
    rule: 'MustRunAsNonRoot'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      # Forbid adding the root group.
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      # Forbid adding the root group.
      - min: 1
        max: 65535
  readOnlyRootFilesystem: false
  seLinux:
    rule: 'RunAsAny'
{{- end }}
{{- end }}
