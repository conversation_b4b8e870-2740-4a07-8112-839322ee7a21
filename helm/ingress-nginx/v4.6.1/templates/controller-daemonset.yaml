{{- if or (eq .Values.controller.kind "DaemonSet") (eq .Values.controller.kind "Both") -}}
{{- include  "isControllerTagValid" . -}}
apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    {{- include "ingress-nginx.labels" . | nindent 4 }}
    app.kubernetes.io/component: controller
    {{- with .Values.controller.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  name: {{ include "ingress-nginx.controller.fullname" . }}
  namespace: {{ .Release.Namespace }}
  {{- if .Values.controller.annotations }}
  annotations: {{ toYaml .Values.controller.annotations | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "ingress-nginx.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: controller
  revisionHistoryLimit: {{ .Values.revisionHistoryLimit }}
  {{- if .Values.controller.updateStrategy }}
  updateStrategy: {{ toYaml .Values.controller.updateStrategy | nindent 4 }}
  {{- end }}
  minReadySeconds: {{ .Values.controller.minReadySeconds }}
  template:
    metadata:
    {{- if .Values.controller.podAnnotations }}
      annotations:
      {{- range $key, $value := .Values.controller.podAnnotations }}
        {{ $key }}: {{ $value | quote }}
      {{- end }}
    {{- end }}
      labels:
        {{- include "ingress-nginx.labels" . | nindent 8 }}
        app.kubernetes.io/component: controller
        {{- with .Values.controller.labels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- if .Values.controller.podLabels }}
        {{- toYaml .Values.controller.podLabels | nindent 8 }}
      {{- end }}
    spec:
    {{- if .Values.controller.dnsConfig }}
      dnsConfig: {{ toYaml .Values.controller.dnsConfig | nindent 8 }}
    {{- end }}
    {{- if .Values.controller.hostname }}
      hostname: {{ toYaml .Values.controller.hostname | nindent 8 }}
    {{- end }}
      dnsPolicy: {{ .Values.controller.dnsPolicy }}
    {{- if .Values.imagePullSecrets }}
      imagePullSecrets: {{ toYaml .Values.imagePullSecrets | nindent 8 }}
    {{- end }}
    {{- if .Values.controller.priorityClassName }}
      priorityClassName: {{ .Values.controller.priorityClassName }}
    {{- end }}
    {{- if or .Values.controller.podSecurityContext .Values.controller.sysctls }}
      securityContext:
    {{- end }}
    {{- if .Values.controller.podSecurityContext  }}
        {{- toYaml .Values.controller.podSecurityContext | nindent 8 }}
    {{- end }}
    {{- if .Values.controller.sysctls }}
        sysctls:
    {{- range $sysctl, $value := .Values.controller.sysctls }}
        - name: {{ $sysctl | quote }}
          value: {{ $value | quote }}
    {{- end }}
    {{- end }}
    {{- if .Values.controller.shareProcessNamespace }}
      shareProcessNamespace: {{ .Values.controller.shareProcessNamespace }}
    {{- end }}
      containers:
        - name: {{ .Values.controller.containerName }}
          {{- with .Values.controller.image }}
          image: "{{- if .repository -}}{{ .repository }}{{ else }}{{ .registry }}/{{ include "ingress-nginx.image" . }}{{- end -}}:{{ .tag }}{{ include "ingress-nginx.imageDigest" . }}"
          {{- end }}
          imagePullPolicy: {{ .Values.controller.image.pullPolicy }}
        {{- if .Values.controller.lifecycle }}
          lifecycle: {{ toYaml .Values.controller.lifecycle | nindent 12 }}
        {{- end }}
          args:
            {{- include "ingress-nginx.params" . | nindent 12 }}
          securityContext: {{ include "controller.containerSecurityContext" . | nindent 12 }}
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          {{- if .Values.controller.enableMimalloc }}
            - name: LD_PRELOAD
              value: /usr/local/lib/libmimalloc.so
          {{- end }}
          {{- if .Values.controller.extraEnvs }}
            {{- toYaml .Values.controller.extraEnvs | nindent 12 }}
          {{- end }}
          {{- if .Values.controller.startupProbe }}
          startupProbe: {{ toYaml .Values.controller.startupProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.controller.livenessProbe }}
          livenessProbe: {{ toYaml .Values.controller.livenessProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.controller.readinessProbe }}
          readinessProbe: {{ toYaml .Values.controller.readinessProbe | nindent 12 }}
          {{- end }}
          ports:
          {{- range $key, $value := .Values.controller.containerPort }}
            - name: {{ $key }}
              containerPort: {{ $value }}
              protocol: TCP
              {{- if $.Values.controller.hostPort.enabled }}
              hostPort: {{ index $.Values.controller.hostPort.ports $key | default $value }}
              {{- end }}
          {{- end }}
          {{- if .Values.controller.metrics.enabled }}
            - name: {{ .Values.controller.metrics.portName }}
              containerPort: {{ .Values.controller.metrics.port }}
              protocol: TCP
          {{- end }}
          {{- if .Values.controller.admissionWebhooks.enabled }}
            - name: webhook
              containerPort: {{ .Values.controller.admissionWebhooks.port }}
              protocol: TCP
          {{- end }}
          {{- range $key, $value := .Values.tcp }}
            - name: {{ if $.Values.portNamePrefix }}{{ $.Values.portNamePrefix }}-{{ end }}{{ $key }}-tcp
              containerPort: {{ $key }}
              protocol: TCP
              {{- if $.Values.controller.hostPort.enabled }}
              hostPort: {{ $key }}
              {{- end }}
          {{- end }}
          {{- range $key, $value := .Values.udp }}
            - name: {{ if $.Values.portNamePrefix }}{{ $.Values.portNamePrefix }}-{{ end }}{{ $key }}-udp
              containerPort: {{ $key }}
              protocol: UDP
              {{- if $.Values.controller.hostPort.enabled }}
              hostPort: {{ $key }}
              {{- end }}
          {{- end }}
        {{- if (or .Values.controller.customTemplate.configMapName .Values.controller.extraVolumeMounts .Values.controller.admissionWebhooks.enabled .Values.controller.extraModules) }}
          volumeMounts:
          {{- if .Values.controller.extraModules }}
            - name: modules
              mountPath: /modules_mount
          {{- end }}
          {{- if .Values.controller.customTemplate.configMapName }}
            - mountPath: /etc/nginx/template
              name: nginx-template-volume
              readOnly: true
          {{- end }}
          {{- if .Values.controller.admissionWebhooks.enabled }}
            - name: webhook-cert
              mountPath: /usr/local/certificates/
              readOnly: true
          {{- end }}
          {{- if .Values.controller.extraVolumeMounts }}
            {{- toYaml .Values.controller.extraVolumeMounts | nindent 12 }}
          {{- end }}
        {{- end }}
        {{- if .Values.controller.resources }}
          resources: {{ toYaml .Values.controller.resources | nindent 12 }}
        {{- end }}
      {{- if .Values.controller.extraContainers }}
        {{ toYaml .Values.controller.extraContainers | nindent 8 }}
      {{- end }}


    {{- if (or .Values.controller.extraInitContainers .Values.controller.extraModules) }}
      initContainers:
      {{- if .Values.controller.extraInitContainers }}
        {{ toYaml .Values.controller.extraInitContainers | nindent 8 }}
      {{- end }}
      {{- if .Values.controller.extraModules }}
        {{- range .Values.controller.extraModules }}
          {{ $containerSecurityContext := .containerSecurityContext | default $.Values.controller.containerSecurityContext }}
{{ include "extraModules" (dict "name" .name "image" .image "containerSecurityContext" $containerSecurityContext) | indent 8 }}
        {{- end }}
      {{- end }}
      {{- if .Values.controller.opentelemetry.enabled}}
          {{ $otelContainerSecurityContext := $.Values.controller.opentelemetry.containerSecurityContext | default $.Values.controller.containerSecurityContext }}
          {{- include "extraModules" (dict "name" "opentelemetry" "image" .Values.controller.opentelemetry.image "containerSecurityContext" $otelContainerSecurityContext) | nindent 8}}
      {{- end}}
    {{- end }}
    {{- if .Values.controller.hostNetwork }}
      hostNetwork: {{ .Values.controller.hostNetwork }}
    {{- end }}
    {{- if .Values.controller.nodeSelector }}
      nodeSelector: {{ toYaml .Values.controller.nodeSelector | nindent 8 }}
    {{- end }}
    {{- if .Values.controller.tolerations }}
      tolerations: {{ toYaml .Values.controller.tolerations | nindent 8 }}
    {{- end }}
    {{- if .Values.controller.affinity }}
      affinity: {{ toYaml .Values.controller.affinity | nindent 8 }}
    {{- end }}
    {{- if .Values.controller.topologySpreadConstraints }}
      topologySpreadConstraints: {{ toYaml .Values.controller.topologySpreadConstraints | nindent 8 }}
    {{- end }}
      serviceAccountName: {{ template "ingress-nginx.serviceAccountName" . }}
      terminationGracePeriodSeconds: {{ .Values.controller.terminationGracePeriodSeconds }}
    {{- if (or .Values.controller.customTemplate.configMapName .Values.controller.extraVolumeMounts .Values.controller.admissionWebhooks.enabled .Values.controller.extraVolumes .Values.controller.extraModules .Values.controller.opentelemetry.enabled) }}
      volumes:
      {{- if (or .Values.controller.extraModules .Values.controller.opentelemetry.enabled)}}
        - name: modules
          emptyDir: {}
      {{- end }}
      {{- if .Values.controller.customTemplate.configMapName }}
        - name: nginx-template-volume
          configMap:
            name: {{ .Values.controller.customTemplate.configMapName }}
            items:
            - key: {{ .Values.controller.customTemplate.configMapKey }}
              path: nginx.tmpl
      {{- end }}
      {{- if .Values.controller.admissionWebhooks.enabled }}
        - name: webhook-cert
          secret:
            secretName: {{ include "ingress-nginx.fullname" . }}-admission
        {{- if .Values.controller.admissionWebhooks.certManager.enabled }}
            items:
              - key: tls.crt
                path: cert
              - key: tls.key
                path: key
        {{- end }}
      {{- end }}
      {{- if .Values.controller.extraVolumes }}
        {{ toYaml .Values.controller.extraVolumes | nindent 8 }}
      {{- end }}
    {{- end }}
{{- end }}
