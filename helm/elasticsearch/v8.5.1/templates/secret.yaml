{{- if .Values.secret.enabled -}}
{{- $passwordValue := (randAlphaNum 16) | b64enc | quote }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "elasticsearch.uname" . }}-credentials
  labels:
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
    chart: "{{ .Chart.Name }}"
    app: "{{ template "elasticsearch.uname" . }}"
    {{- range $key, $value := .Values.labels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
type: Opaque
data:
  username: {{ "elastic" | b64enc }}
  {{- if .Values.secret.password }}
  password: {{ .Values.secret.password | b64enc }}
  {{- else }}
  password: {{ $passwordValue }}
  {{- end }}
{{- end }}
