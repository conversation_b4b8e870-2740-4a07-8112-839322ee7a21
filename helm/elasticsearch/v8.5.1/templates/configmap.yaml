{{- if .Values.esConfig }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "elasticsearch.uname" . }}-config
  labels:
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
    chart: "{{ .Chart.Name }}"
    app: "{{ template "elasticsearch.uname" . }}"
data:
{{- range $path, $config := .Values.esConfig }}
  {{ $path }}: |
{{ $config | indent 4 -}}
{{- end -}}
{{- end -}}
{{- if .Values.esJvmOptions }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "elasticsearch.uname" . }}-jvm-options
  labels:
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
    chart: "{{ .Chart.Name }}"
    app: "{{ template "elasticsearch.uname" . }}"
data:
{{- range $path, $config := .Values.esJvmOptions }}
  {{ $path }}: |
{{ $config | indent 4 -}}
{{- end -}}
{{- end -}}