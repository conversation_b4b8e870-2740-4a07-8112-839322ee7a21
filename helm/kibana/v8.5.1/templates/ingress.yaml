{{- if .Values.ingress.enabled -}}
{{- $fullName := include "kibana.fullname" . -}}
{{- $httpPort := .Values.httpPort -}}
{{- $pathtype := .Values.ingress.pathtype -}}
{{- $ingressPath := .Values.ingress.path -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels: {{ include "kibana.labels" . | nindent 4 }}
{{- with .Values.ingress.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  {{- if .Values.ingress.className }}
  ingressClassName: {{ .Values.ingress.className | quote }}
  {{- end }}
{{- if .Values.ingress.tls }}
  tls:
  {{- if .ingressPath }}
  {{- range .Values.ingress.tls }}
  - hosts:
    {{- range .hosts }}
      - {{ . }}
    {{- end }}
    secretName: {{ .secretName }}
  {{- end }}
{{- else }}
{{ toYaml .Values.ingress.tls | indent 4 }}
  {{- end }}
{{- end }}
  rules:
  {{- range .Values.ingress.hosts }}
    {{- if $ingressPath }}
  - host: {{ . }}
    http:
      paths:
      - path: {{ $ingressPath }}
        pathType: {{ $pathtype }}
        backend:
          service:
            name: {{ $fullName }}
            port: 
              number: {{ $httpPort }}
  {{- else }}
  - host: {{ .host }}
    http:
      paths:
      {{- range .paths }}
      - path: {{ .path }}
        pathType: {{ $pathtype }}
        backend:
          service:
            name: {{ $fullName }}
            port:
              number: {{ .servicePort | default $httpPort }}
      {{- end }}
  {{- end }}
 {{- end }}
 {{- end }}
