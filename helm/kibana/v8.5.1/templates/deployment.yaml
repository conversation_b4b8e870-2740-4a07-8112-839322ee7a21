apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "kibana.fullname" . }}
  labels: {{ include "kibana.labels" . | nindent 4 }}
  {{- if .Values.annotations }}
  annotations:
    {{- range $key, $value := .Values.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  replicas: {{ .Values.replicas }}
  strategy:
{{ toYaml .Values.updateStrategy | indent 4 }}
  selector:
    matchLabels:
      app: {{ .Chart.Name }}
      release: {{ .Release.Name | quote }}
  template:
    metadata:
      labels:
        app: {{ .Chart.Name }}
        release: {{ .Release.Name | quote }}
        {{- range $key, $value := .Values.labels }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
      annotations:
        {{- range $key, $value := .Values.podAnnotations }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
        {{/* This forces a restart if the configmap has changed */}}
        {{- if .Values.kibanaConfig }}
        configchecksum: {{ include (print .Template.BasePath "/configmap.yaml") . | sha256sum | trunc 63 }}
        {{- end }}
    spec:
      automountServiceAccountToken: {{ .Values.automountToken }}
{{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName }}
{{- end }}
      securityContext:
{{ toYaml .Values.podSecurityContext | indent 8 }}
      {{- if .Values.serviceAccount }}
      serviceAccount: {{ .Values.serviceAccount }}
      {{- end }}
      {{- if .Values.hostAliases }}
      hostAliases: {{ toYaml .Values.hostAliases | nindent 6 }}
      {{- end }}
      volumes:
        - name: kibana-tokens
          emptyDir: {}
        - name: elasticsearch-certs
          secret:
            secretName: {{ .Values.elasticsearchCertificateSecret }}
        {{- if .Values.kibanaConfig }}
        - name: kibanaconfig
          configMap:
            name: {{ template "kibana.fullname" . }}-config
        {{- end }}
        {{- range .Values.secretMounts }}
        - name: {{ .name }}
          secret:
            secretName: {{ .secretName }}
        {{- end }}
        {{- if .Values.extraVolumes }}
{{ toYaml .Values.extraVolumes | indent 8 }}
        {{- end }}
    {{- with .Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
{{ toYaml .Values.imagePullSecrets | indent 8 }}
    {{- end }}
      initContainers:
      {{- if .Values.extraInitContainers }}
      # Currently some extra blocks accept strings
      # to continue with backwards compatibility this is being kept
      # whilst also allowing for yaml to be specified too.
      {{- if eq "string" (printf "%T" .Values.extraInitContainers) }}
{{ tpl .Values.extraInitContainers . | indent 6 }}
      {{- else }}
{{ toYaml .Values.extraInitContainers | indent 6 }}
      {{- end }}
      {{- end }}
      containers:
      - name: kibana
        securityContext:
{{ toYaml .Values.securityContext | indent 10 }}
        image: "{{ .Values.image }}:{{ .Values.imageTag }}"
        imagePullPolicy: "{{ .Values.imagePullPolicy }}"
        env:
          {{- if .Values.elasticsearchURL }}
          - name: ELASTICSEARCH_URL
            value: "{{ .Values.elasticsearchURL }}"
          {{- else if .Values.elasticsearchHosts }}
          - name: ELASTICSEARCH_HOSTS
            value: "{{ .Values.elasticsearchHosts }}"
          {{- end }}
          - name: ELASTICSEARCH_SSL_CERTIFICATEAUTHORITIES
            value: "{{ template "kibana.home_dir" . }}/config/certs/{{ .Values.elasticsearchCertificateAuthoritiesFile }}"
          - name: SERVER_HOST
            value: "{{ .Values.serverHost }}"
          - name: ELASTICSEARCH_SERVICEACCOUNTTOKEN
            valueFrom:
              secretKeyRef:
                name: {{ template "kibana.fullname" . }}-es-token
                key: token
                optional: false
{{- if .Values.extraEnvs }}
{{ toYaml .Values.extraEnvs | indent 10 }}
{{- end }}
{{- if .Values.envFrom }}
        envFrom:
{{ toYaml .Values.envFrom | indent 10 }}
{{- end }}
        readinessProbe:
{{ toYaml .Values.readinessProbe | indent 10 }}
          exec:
            command:
              - bash
              - -c
              - |
                #!/usr/bin/env bash -e

                # Disable nss cache to avoid filling dentry cache when calling curl
                # This is required with Kibana Docker using nss < 3.52
                export NSS_SDB_USE_CACHE=no

                http () {
                    local path="${1}"
                    set -- -XGET -s --fail -L

                    if [ -n "${ELASTICSEARCH_USERNAME}" ] && [ -n "${ELASTICSEARCH_PASSWORD}" ]; then
                      set -- "$@" -u "${ELASTICSEARCH_USERNAME}:${ELASTICSEARCH_PASSWORD}"
                    fi

                    STATUS=$(curl --output /dev/null --write-out "%{http_code}" -k "$@" "{{ .Values.protocol }}://localhost:{{ .Values.httpPort }}${path}")
                    if [[ "${STATUS}" -eq 200 ]]; then
                      exit 0
                    fi

                    echo "Error: Got HTTP code ${STATUS} but expected a 200"
                    exit 1
                }

                http "{{ .Values.healthCheckPath }}"
        ports:
        - containerPort: {{ .Values.httpPort }}
{{- if .Values.lifecycle }}
        lifecycle:
{{ toYaml .Values.lifecycle | indent 10 }}
{{- end }}
        resources:
{{ toYaml .Values.resources | indent 10 }}
        volumeMounts:
          - name: elasticsearch-certs
            mountPath: {{ template "kibana.home_dir" . }}/config/certs
            readOnly: true
          - name: kibana-tokens
            mountPath: {{ template "kibana.home_dir" . }}/config/tokens
            readOnly: true
          {{- range .Values.secretMounts }}
          - name: {{ .name }}
            mountPath: {{ .path }}
            {{- if .subPath }}
            subPath: {{ .subPath }}
            {{- end }}
          {{- end }}
          {{- range $path, $config := .Values.kibanaConfig }}
          - name: kibanaconfig
            mountPath: {{ template "kibana.home_dir" . }}/config/{{ $path }}
            subPath: {{ $path }}
          {{- end -}}
        {{- if .Values.extraVolumeMounts }}
{{ toYaml .Values.extraVolumeMounts | indent 10 }}
        {{- end }}
      {{- if .Values.extraContainers }}
      # Currently some extra blocks accept strings
      # to continue with backwards compatibility this is being kept
      # whilst also allowing for yaml to be specified too.
      {{- if eq "string" (printf "%T" .Values.extraContainers) }}
{{ tpl .Values.extraContainers . | indent 6 }}
      {{- else }}
{{ toYaml .Values.extraContainers | indent 6 }}
      {{- end }}
      {{- end }}
