---
apiVersion: v1
kind: Service
metadata:
  name: {{ template "kibana.fullname" . }}
  labels: {{ include "kibana.labels" . | nindent 4 }}
{{- if .Values.service.labels }}
{{ toYaml .Values.service.labels | indent 4}}
{{- end }}
{{- with .Values.service.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  type: {{ .Values.service.type }}
{{- if .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
{{- end }}
{{- with .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
{{ toYaml . | indent 4 }}
{{- end }}
  ports:
    - port: {{ .Values.service.port }}
{{- if .Values.service.nodePort }}
      nodePort: {{ .Values.service.nodePort }}
{{- end }}
      protocol: TCP
      name: {{ .Values.service.httpPortName | default "http" }}
      targetPort: {{ .Values.httpPort }}
  selector:
    app: {{ .Chart.Name }}
    release: {{ .Release.Name | quote }}
