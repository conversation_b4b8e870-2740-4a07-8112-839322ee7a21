apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: pre-install-{{ template "kibana.fullname" . }}
  labels: {{ include "kibana.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": hook-succeeded
    {{- if .Values.annotations }}
    {{- range $key, $value := .Values.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
    {{- end }}
subjects:
  - kind: ServiceAccount
    name: pre-install-{{ template "kibana.fullname" . }}
    namespace: {{ .Release.Namespace | quote }}
roleRef:
  kind: Role
  name: pre-install-{{ template "kibana.fullname" . }}
  apiGroup: rbac.authorization.k8s.io
