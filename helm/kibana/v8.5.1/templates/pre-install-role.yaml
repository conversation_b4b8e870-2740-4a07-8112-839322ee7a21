apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: pre-install-{{ template "kibana.fullname" . }}
  labels: {{ include "kibana.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-delete-policy": hook-succeeded
    {{- if .Values.annotations }}
    {{- range $key, $value := .Values.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
    {{- end }}
rules:
  - apiGroups:
      - ""
    resources:
      - secrets
    verbs:
      - create
      - update
