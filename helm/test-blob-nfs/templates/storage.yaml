---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: pv-blob-nfs
spec:
  capacity:
    storage: 1Pi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: azureblob-nfs-premium
  csi:
    driver: blob.csi.azure.com
    readOnly: false
    # make sure volumeid is unique for every identical storage blob container in the cluster
    # character `#` is reserved for internal use and cannot be used in volumehandle
    volumeHandle: {{ uuidv4 }}
    volumeAttributes:
      resourceGroup: {{ .Values.storage.resourceGroup }}
      storageAccount: {{ .Values.storage.storageAccountName }}
      containerName: {{ .Values.storage.containerName }}
      protocol: nfs
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: pvc-blob-nfs
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  volumeName: pv-blob-nfs
  storageClassName: azureblob-nfs-premium