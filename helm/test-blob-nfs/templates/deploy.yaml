---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-blob-nfs
  namespace: test
  labels:
    app: nginx-blob-nfs
spec:
  selector:
    matchLabels:
      app: nginx-blob-nfs
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: nginx-blob-nfs
    spec:
      containers:
        - image: mcr.microsoft.com/oss/nginx/nginx:1.17.3-alpine
          name: nginx-blob-nfs
          command:
            - "/bin/sh"
            - "-c"
            - while true; do sleep 1; done
          volumeMounts:
            - name: blob-nfs
              mountPath: "/mnt/blob-nfs"
      volumes:
        - name: blob-nfs
          persistentVolumeClaim:
            claimName: pvc-blob-nfs
