---
apiVersion: v1
kind: Secret
metadata:
  name: stac-secret
  namespace: default
type: Opaque
data:
  storageaccountname: {{ .Values.storage.storageAccountName | b64enc | quote }}
  azurestorageaccountkey: {{ .Values.storage.key | b64enc | quote }}
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: pv-file-nfs
spec:
  capacity:
    storage: 1Ti
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: azurefile-csi-premium
  csi:
    driver: file.csi.azure.com
    readOnly: false
    # make sure volumeid is unique for every identical storage blob container in the cluster
    # character `#` is reserved for internal use and cannot be used in volumehandle
    volumeHandle: {{ uuidv4 }}
    volumeAttributes:
      resourceGroup: {{ .Values.storage.resourceGroup }}
      storageAccount: {{ .Values.storage.storageAccountName }}
      shareName: {{ .Values.storage.shareName }}
      protocol: nfs
    nodeStageSecretRef:
      name: stac-secret
      namespace: default
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: pvc-file-nfs
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  volumeName: pv-file-nfs
  storageClassName: azurefile-csi-premium