---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-file-nfs
  namespace: test
  labels:
    app: nginx-file-nfs
spec:
  selector:
    matchLabels:
      app: nginx-file-nfs
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: nginx-file-nfs
    spec:
      containers:
        - image: mcr.microsoft.com/oss/nginx/nginx:1.17.3-alpine
          name: nginx-file-nfs
          command:
            - "/bin/sh"
            - "-c"
            - while true; do sleep 1; done
          volumeMounts:
            - name: file-nfs
              mountPath: "/mnt/file-nfs"
      volumes:
        - name: file-nfs
          persistentVolumeClaim:
            claimName: pvc-file-nfs
