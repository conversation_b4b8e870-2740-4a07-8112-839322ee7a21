---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-azuredisk
  namespace: test
  labels:
    app: nginx
spec:
  selector:
    matchLabels:
      app: nginx
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
        - image: mcr.microsoft.com/oss/nginx/nginx:1.17.3-alpine
          name: nginx-azuredisk
          command:
            - "/bin/sh"
            - "-c"
            - while true; do sleep 1; done
          volumeMounts:
            - name: azuredisk01
              mountPath: "/mnt/azuredisk"
      volumes:
        - name: azuredisk01
          persistentVolumeClaim:
            claimName: pvc-azuredisk
