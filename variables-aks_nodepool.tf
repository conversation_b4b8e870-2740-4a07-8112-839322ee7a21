
variable "aks_nodepool" {
  description = <<EOT
  (Optional) The AKS Node Pools to be created

  GENERAL

  `name`  - (Required) The name of the Node Pool which should be created within the Kubernetes Cluster.
            A Windows Node Pool cannot have a name longer than 6 characters.
            Name needs to be unique within a deployment.
            Changing this forces a new resource to be created.

  `temporary_name_for_rotation` - (Optional) Specifies the name of the temporary node pool used to cycle the default node pool for VM resizing.

  `mode`  - (Optional) Should this Node Pool be used for System or User resources?
            Possible values are System and User.
            Defaults to User.

  `kubernetes_version`  - (Optional) Version of Kubernetes used for the Agents.
                          If not specified, the latest recommended version will be used at provisioning time (but won't auto-upgrade).
                          AKS does not require an exact patch version to be specified, minor version aliases such as 1.22 are also supported.
                          The minor version's latest GA patch is automatically chosen in that case.
                          This version must be supported by the Kubernetes Cluster - as such the version of Kubernetes used on the Cluster/Control Plane may need to be upgraded first.
  
  `nodepool_subnet_id`  - (Optional) ID of the subnet where this Node Pool should exist. Changing this forces a new resource to be created.
                      At this time the Virtual Network must be the same for all node pools in the cluster.
                      A route table must be configured on this Subnet.
                      If not defined it defaults to the subnet_object used for system nodepool.

  OPTIONAL

  `host_encryption_enabled`   - (Optional) Should the nodes in this Node Pool have host encryption enabled? Defaults to true.

  `node_public_ip_enabled`    - (Optional) Should each node have a Public IP Address? Defaults to false. Changing this forces a new resource to be created.

  `node_public_ip_prefix_id`  - (Optional) Resource ID for the Public IP Addresses Prefix for the nodes in this Node Pool. `node_public_ip_enabled` should be true. Changing this forces a new resource to be created.

  `fips_enabled`              - (Optional) Should the nodes in this Node Pool have Federal Information Processing Standard enabled? Changing this forces a new resource to be created.
                                FIPS support is in Public Preview.
                                Information and details on how to opt into the Preview can be found at https://learn.microsoft.com/en-us/azure/aks/use-multiple-node-pools#add-a-fips-enabled-node-pool-preview.

  `workload_runtime`         - (Optional) Used to specify the workload runtime. Allowed values are OCIContainer, WasmWasi and KataMshvVmIsolation.

                                WebAssembly System Interface node pools are in Public Preview.
                                Information and details on how to opt into the preview can be found at https://learn.microsoft.com/en-us/azure/aks/use-wasi-node-pools.

                                Pod Sandboxing / KataVM Isolation node pools are in Public Preview.
                                Information and details on how to opt into the preview can be found at https://learn.microsoft.com/en-us/azure/aks/use-pod-sandboxing.

  KUBELET

  `kubelet_disk_type`        - (Optional) The type of disk used by kubelet. Possible values are OS and Temporary. Defaults to OS.

  `kubelet_config`           - (Optional) A kubelet_config block as defined below.

    `kubelet_config_allowed_unsafe_sysctls`    - (Optional) Specifies the allow list of unsafe sysctls command or patterns (ending in *). Changing this forces a new resource to be created.

    `kubelet_config_container_log_max_line`    - (Optional) Specifies the maximum number of container log files that can be present for a container. must be at least 2. Changing this forces a new resource to be created.

    `kubelet_config_container_log_max_size_mb` - (Optional) Specifies the maximum size (e.g. 10MB) of container log file before it is rotated. Changing this forces a new resource to be created.

    `kubelet_config_cpu_manager_policy`        - (Optional) Specifies the CPU Manager policy to use. Possible values are none and static, Changing this forces a new resource to be created.

    `kubelet_config_cpu_cfs_quota_enabled`     - (Optional) Is CPU CFS quota enforcement for containers enabled? Changing this forces a new resource to be created.

    `kubelet_config_cpu_cfs_quota_period`      - (Optional) Specifies the CPU CFS quota period value. Changing this forces a new resource to be created.

    `kubelet_config_image_gc_high_threshold`   - (Optional) Specifies the percent of disk usage above which image garbage collection is always run. Must be between 0 and 100. Changing this forces a new resource to be created.

    `kubelet_config_image_gc_low_threshold`    - (Optional) Specifies the percent of disk usage lower than which image garbage collection is never run. Must be between 0 and 100. Changing this forces a new resource to be created.

    `kubelet_config_pod_max_pid`               - (Optional) Specifies the maximum number of processes per pod. Changing this forces a new resource to be created.

    `kubelet_config_topology_manager_policy`    - (Optional) Specifies the Topology Manager policy to use. Possible values are none, best-effort, restricted or single-numa-node. Changing this forces a new resource to be created.

  AUTOSCALING

  `auto_scaling_enabled` - (Optional) Whether to enable auto-scaler. Defaults to false.

  `min_count`            - (Optional) The minimum number of nodes which should exist within this Node Pool. Valid values are between 0 and 1000 and must be less than or equal to max_count.

  `max_count`            - (Optional) The maximum number of nodes which should exist within this Node Pool. Valid values are between 0 and 1000 and must be greater than or equal to min_count.

  `node_count`           - (Optional) The initial number of nodes which should exist within this Node Pool. Valid values are between 0 and 1000 (inclusive) for user pools and between 1 and 1000 (inclusive) for system pools and must be a value in the range min_count - max_count.

  UPGRADE

  `max_surge` - (Optional) The maximum number or percentage of nodes which will be added to the Node Pool size during an upgrade.
                If a percentage is provided, the number of surge nodes is calculated from the current node count on the cluster.
                Node surge can allow a cluster to have more nodes than max_count during an upgrade.
                Ensure that your cluster has enough IP space during an upgrade.

  NODES

  `vm_size`                      - (Required) The SKU which should be used for the Virtual Machines used in this Node Pool. Changing this forces a new resource to be created.

  `zones`                        - (Optional) Specifies a list of Availability Zones in which this Kubernetes Cluster Node Pool should be located. Changing this forces a new Kubernetes Cluster Node Pool to be created.

  `os_type`                      - (Optional) The Operating System which should be used for this Node Pool. Changing this forces a new resource to be created. Possible values are Linux and Windows. Defaults to Linux.

  `os_sku`                       - (Optional) OsSKU to be used to specify Linux OSType. Not applicable to Windows OSType. Possible values include: Ubuntu, CBLMariner. Defaults to Ubuntu. Changing this forces a new resource to be created.

  `ultra_ssd_enabled`            - (Optional) Used to specify whether the UltraSSD is enabled in the Node Pool. Defaults to false.
                                    Check https://learn.microsoft.com/en-us/azure/aks/use-ultra-disks for more information.

  `os_disk_type`                 - (Optional) The type of disk which should be used for the Operating System. Possible values are Ephemeral and Managed. Defaults to Managed. Changing this forces a new resource to be created.

  `os_disk_size_gb`              - (Optional) The Agent Operating System disk size in GB. Changing this forces a new resource to be created.

  `proximity_placement_group_id` - (Optional) The ID of the Proximity Placement Group where the Virtual Machine Scale Set that powers this Node Pool will be placed. Changing this forces a new resource to be created.

  `scale_down_mode`              - (Optional) Specifies how the node pool should deal with scaled-down nodes. Allowed values are Delete and Deallocate. Defaults to Delete.

  `priority`                     - (Optional) The Priority for Virtual Machines within the Virtual Machine Scale Set that powers this Node Pool. Possible values are Regular and Spot. Defaults to Regular.
                                    When setting priority to Spot - you must configure an `eviction_policy` and `spot_max_price`.
                                    Also the `node_labels` and `node_taints` needs to be added as per https://learn.microsoft.com/en-us/azure/aks/spot-node-pool.
                                    Changing this forces a new resource to be created.

  `eviction_policy`              - (Optional) The Eviction Policy which should be used for Virtual Machines within the Virtual Machine Scale Set powering this Node Pool. Possible values are Deallocate and Delete. Changing this forces a new resource to be created.

  `spot_max_price`               - (Optional) The maximum price you're willing to pay in USD per Virtual Machine. Valid values are -1 (the current on-demand price for a Virtual Machine) or a positive value with up to five decimal places. Changing this forces a new resource to be created.

  `pod_subnet_id`                - (Optional) The ID of the Subnet where the pods in the Node Pool should exist. Changing this forces a new resource to be created.

  `max_pods`                     - (Optional) The maximum number of pods that can run on each agent. Changing this forces a new resource to be created.

  `node_labels`                  - (Optional) A map of Kubernetes labels which should be applied to nodes in this Node Pool.

  `node_taints`                  - (Optional) A list of Kubernetes taints which should be applied to nodes in the agent pool (e.g key=value:NoSchedule). Changing this forces a new resource to be created.

  CUSTOM NODE CONFIG

    File Handle Limits

    `custom_node_config_file_handle_max`  - (Optional) The sysctl setting fs.file-max. Must be between 8192 and 12000500. Changing this forces a new resource to be created.

    `custom_node_config_file_number_open` - (Optional) The sysctl setting fs.nr_open. Must be between 8192 and 20000500. Changing this forces a new resource to be created.

    `custom_node_config_file_inotify_max` - (Optional) The sysctl setting fs.inotify.max_user_watches. Must be between 781250 and 2097152. Changing this forces a new resource to be created.

    `custom_node_config_file_aio_max`     - (Optional) The sysctl setting fs.aio-max-nr. Must be between 65536 and 6553500. Changing this forces a new resource to be created.

    Worker Limits

    `custom_node_config_kernel_threads_max` - (Optional) The sysctl setting kernel.threads-max. Must be between 20 and 513785. Changing this forces a new resource to be created.

    Socket and Network Tuning

      NET Core

      `custom_node_config_network_connection_max`                - (Optional) The sysctl setting net.core.somaxconn. Must be between 4096 and 3240000. Changing this forces a new resource to be created.

      `custom_node_config_network_dev_backlog_max`               - (Optional) The sysctl setting net.core.netdev_max_backlog. Must be between 1000 and 3240000. Changing this forces a new resource to be created.

      `custom_node_config_network_socket_receive_buffer_default` - (Optional) The sysctl setting net.core.rmem_default. Must be between 212992 and 134217728. Changing this forces a new resource to be created.

      `custom_node_config_network_socket_receive_buffer_max`     - (Optional) The sysctl setting net.core.rmem_max. Must be between 212992 and 134217728. Changing this forces a new resource to be created.

      `custom_node_config_network_socket_send_buffer_default`    - (Optional) The sysctl setting net.core.wmem_default. Must be between 212992 and 134217728. Changing this forces a new resource to be created.

      `custom_node_config_network_socket_send_buffer_max`        - (Optional) The sysctl setting net.core.wmem_max. Must be between 212992 and 134217728. Changing this forces a new resource to be created.

      `custom_node_config_network_socket_option_memory_max`      - (Optional) The sysctl setting net.core.optmem_max. Must be between 20480 and 4194304. Changing this forces a new resource to be created.

      NET IPv4

      `custom_node_config_network_ipv4_connection_request_backlog_max` - (Optional) The sysctl setting net.ipv4.tcp_max_syn_backlog. Must be between 128 and 3240000. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_timewait_bucket`                - (Optional) The sysctl setting net.ipv4.tcp_max_tw_buckets. Must be between 8000 and 1440000. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_timewait_reuse`                 - (Optional) Is sysctl setting net.ipv4.tcp_tw_reuse enabled? Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_fin_timeout`                    - (Optional) The sysctl setting net.ipv4.tcp_fin_timeout. Must be between 5 and 120. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_tcp_keepalive_timeout`          - (Optional) The sysctl setting net.ipv4.tcp_keepalive_time. Must be between 30 and 432000. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_tcp_keepalive_probes`           - (Optional) The sysctl setting net.ipv4.tcp_keepalive_probes. Must be between 1 and 15. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_tcp_probe_interval`             - (Optional) The sysctl setting net.ipv4.tcp_keepalive_intvl. Must be between 10 and 90. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_local_port_range_min`           - (Optional) The sysctl setting net.ipv4.ip_local_port_range min value. Must be between 1024 and 60999. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_local_port_range_max`           - (Optional) The sysctl setting net.ipv4.ip_local_port_range max value. Must be between 32768 and 65535. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_arp_cache_gc_min`               - (Optional) The sysctl setting net.ipv4.neigh.default.gc_thresh1. Must be between 128 and 80000. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_arp_cache_gc_soft_max`          - (Optional) The sysctl setting net.ipv4.neigh.default.gc_thresh2. Must be between 512 and 90000. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_arp_cache_gc_max`               - (Optional) The sysctl setting net.ipv4.neigh.default.gc_thresh3. Must be between 1024 and 100000. Changing this forces a new resource to be created.

      NET NetFilter

      `custom_node_config_network_ipv4_nat_connection_max` - (Optional) The sysctl setting net.netfilter.nf_conntrack_max. Must be between 131072 and 2097152. Changing this forces a new resource to be created.

      `custom_node_config_network_ipv4_nat_bucket_max`     - (Optional) The sysctl setting net.netfilter.nf_conntrack_buckets. Must be between 65536 and 147456. Changing this forces a new resource to be created.

      Virtual Memory

      `custom_node_config_vm_max_map_count`       - (Optional) The sysctl setting vm.max_map_count. Must be between 65530 and 262144. Changing this forces a new resource to be created.

      `custom_node_config_vm_swappiness`          - (Optional) The sysctl setting vm.swappiness. Must be between 0 and 100. Changing this forces a new resource to be created.

      `custom_node_config_vm_vfs_cache_pressure`  - (Optional) The sysctl setting vm.vfs_cache_pressure. Must be between 0 and 100. Changing this forces a new resource to be created.

      Swap

      `custom_node_config_swap_file_size_mb` - (Optional) Specifies the size of swap file on each node in MB. Changing this forces a new resource to be created.

      Huge Pages

      `custom_node_config_transparent_huge_page_enabled` - (Optional) Specifies the Transparent Huge Page enabled configuration. Possible values are always, madvise and never. Changing this forces a new resource to be created.

      `custom_node_config_transparent_huge_page_defrag`  - (Optional) specifies the defrag configuration for Transparent Huge Page. Possible values are always, defer, defer+madvise, madvise and never. Changing this forces a new resource to be created.

  TIMEOUTS

  `timeout_create` - (Optional) Used when creating the Kubernetes Cluster Node Pool. Defaults to 30 minutes.

  `timeout_update` - (Optional) Used when updating the Kubernetes Cluster Node Pool. Defaults to 30 minutes.

  `timeout_read`   - (Optional) Used when retrieving the Kubernetes Cluster Node Pool. Defaults to 5 minutes.

  `timeout_delete` - (Optional) Used when deleting the Kubernetes Cluster Node Pool. Defaults to 30 minutes.

  TAGS

  `custom_tags` - (Optional) A mapping of custom tags which should be appended to the default tags.

  EOT
  type = set(object({
    #General
    name               = string
    temporary_name_for_rotation = optional(string)
    mode               = optional(string, "User")
    kubernetes_version = optional(string)
    nodepool_subnet_id = optional(string)

    #Optional
    host_encryption_enabled  = optional(bool, true)
    node_public_ip_enabled   = optional(bool, false)
    node_public_ip_prefix_id = optional(string)
    fips_enabled             = optional(bool, false)
    workload_runtime         = optional(string, "OCIContainer")

    #Autoscaling
    auto_scaling_enabled = optional(bool, false)
    min_count            = optional(number)
    max_count            = optional(number)
    node_count           = optional(number)

    #Upgrade
    max_surge = optional(string, "100%")
    drain_timeout_in_minutes = optional(string, null)
    node_soak_duration_in_minutes = optional(string, null)

    #Nodes
    vm_size                      = string
    zones                        = optional(list(string))
    os_type                      = optional(string, "Linux")
    os_sku                       = optional(string)
    ultra_ssd_enabled            = optional(bool, false)
    os_disk_type                 = optional(string, "Managed")
    os_disk_size_gb              = optional(number)
    proximity_placement_group_id = optional(string)
    scale_down_mode              = optional(string, "Delete")
    priority                     = optional(string, "Regular")
    eviction_policy              = optional(string)
    spot_max_price               = optional(string)
    pod_subnet_id                = optional(string)
    max_pods                     = optional(number, 50)
    node_labels                  = optional(map(string))
    node_taints                  = optional(list(string))

    #Kubelet
    kubelet_disk_type                        = optional(string, "OS")
    kubelet_config_allowed_unsafe_sysctls    = optional(list(string), [])
    kubelet_config_container_log_max_line    = optional(number)
    kubelet_config_container_log_max_size_mb = optional(number)
    kubelet_config_cpu_manager_policy        = optional(string, "none")
    kubelet_config_cpu_cfs_quota_enabled     = optional(bool, false)
    kubelet_config_cpu_cfs_quota_period      = optional(string)
    kubelet_config_image_gc_high_threshold   = optional(number, 85)
    kubelet_config_image_gc_low_threshold    = optional(number, 80)
    kubelet_config_pod_max_pid               = optional(number)
    kubelet_config_topology_manager_policy   = optional(string, "none")

    #Custom Node Config
    #File Handle Limits
    custom_node_config_file_handle_max  = optional(number, 709620)
    custom_node_config_file_number_open = optional(number, 1048576)
    custom_node_config_file_inotify_max = optional(number, 1048576)
    custom_node_config_file_aio_max     = optional(number, 65536)
    #Worker Limits
    custom_node_config_kernel_threads_max = optional(number, 55601)
    #Socket and Network Tuning
    #net_core
    custom_node_config_network_connection_max                = optional(number, 16384)
    custom_node_config_network_dev_backlog_max               = optional(number, 1000)
    custom_node_config_network_socket_receive_buffer_default = optional(number, 212992)
    custom_node_config_network_socket_receive_buffer_max     = optional(number, 212992)
    custom_node_config_network_socket_send_buffer_default    = optional(number, 212992)
    custom_node_config_network_socket_send_buffer_max        = optional(number, 212992)
    custom_node_config_network_socket_option_memory_max      = optional(number, 20480)
    #net_ipv4
    custom_node_config_network_ipv4_connection_request_backlog_max = optional(number, 16384)
    custom_node_config_network_ipv4_timewait_bucket                = optional(number, 32768)
    custom_node_config_network_ipv4_timewait_reuse                 = optional(bool, false)
    custom_node_config_network_ipv4_fin_timeout                    = optional(number, 60)
    custom_node_config_network_ipv4_tcp_keepalive_timeout          = optional(number, 7200)
    custom_node_config_network_ipv4_tcp_keepalive_probes           = optional(number, 9)
    custom_node_config_network_ipv4_tcp_probe_interval             = optional(number, 75)
    custom_node_config_network_ipv4_local_port_range_min           = optional(number, 32768)
    custom_node_config_network_ipv4_local_port_range_max           = optional(number, 60999)
    custom_node_config_network_ipv4_arp_cache_gc_min               = optional(number, 4096)
    custom_node_config_network_ipv4_arp_cache_gc_soft_max          = optional(number, 8192)
    custom_node_config_network_ipv4_arp_cache_gc_max               = optional(number, 16384)
    #net_filter
    custom_node_config_network_ipv4_nat_connection_max = optional(number, 131072)
    custom_node_config_network_ipv4_nat_bucket_max     = optional(number, 65536)
    #virtual memory
    custom_node_config_vm_max_map_count      = optional(number, 65530)
    custom_node_config_vm_swappiness         = optional(number, 60)
    custom_node_config_vm_vfs_cache_pressure = optional(number, 100)
    #swap
    custom_node_config_swap_file_size_mb = optional(string)
    #huge pages
    custom_node_config_transparent_huge_page_enabled = optional(string)
    custom_node_config_transparent_huge_page_defrag  = optional(string)
    #Timeouts
    timeout_create = optional(string)
    timeout_update = optional(string)
    timeout_read   = optional(string)
    timeout_delete = optional(string)
    #Tags
    custom_tags = optional(map(string))
  }))
  default = null
}

#### Global Variables - Applied on all nodepools ####
#Timeouts
variable "aks_nodepool_timeout_create" {
  description = "Used when creating the Kubernetes Cluster Node Pool. Defaults to 30 minutes."
  type        = string
  default     = "30m"
}
variable "aks_nodepool_timeout_update" {
  description = "Used when updating the Kubernetes Cluster Node Pool. Defaults to 30 minutes."
  type        = string
  default     = "30m"
}
variable "aks_nodepool_timeout_read" {
  description = "Used when retrieving the Kubernetes Cluster Node Pool. Defaults to 5 minutes."
  type        = string
  default     = "5m"
}
variable "aks_nodepool_timeout_delete" {
  description = "Used when deleting the Kubernetes Cluster Node Pool. Defaults to 30 minutes."
  type        = string
  default     = "30m"
}

