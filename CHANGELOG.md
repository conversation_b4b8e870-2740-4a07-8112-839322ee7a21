## Azure Kubernetes Services
### v4.0.2 - September 22, 2025 [current]
ENHANCEMENTS:
- [CCE-8653] Added support for max_pods variable in the system node pool

### v4.0.1 - August 28, 2025
ENHANCEMENTS:
- [CCE-8319] Add Drain timeout and node soak duration for sys and user node pools

### v4.0.0 - August 25, 2025
BUGFIXES:
- Wrong version used

### v3.0.1 - August 21, 2025 [deprecated]
FEATURES:
- [CCE-8238] Added support for temporary_name_for_rotation in user node pools
- [CCE-8239] Added support for orchestrator_version
- Updated provider version to >= 4.16.0

### v3.0.0 - July 07, 2025
FEATURES:
- [CCE-7878] Added support automated upgrading configurations
- Switch default network plugin to Azure CNI Overlay by default instead of deprecated kubenet
- [CCE-7985] Added support for custom data flows for container insights
ENHANCEMENTS:
- [CCE-7981] no_proxy change handled by AKS and Terraform without cluster destroy. Previous workaround is removed.

<div style="background-color: #A61F38; border-radius: 6px; padding: 1rem; color: white;">
Please note: if you are upgrading an EXISTING AKS cluster to version >2.3.1, you must add the following lines to your code to avoid cluster redeployment:
aks_network_plugin = „kubenet”
aks_network_plugin_mode = null
</div>

### v2.3.1 - June 23, 2025
BUGFIXES:
- Added a missing required variable to the DEV-demo01 example 01-default - CCE-8042

### v2.3.0 - May 27, 2025
FEATURES:
- Added new variable (only_critical_addons_enabled) to support system node pool isolation in the Default Node Pool
- Added new variable (temporary_name_for_rotation) for only_critical_addons_enabled to work properly. Defaults to the previous hardcoded name: "systemtmp"

### v2.2.1 - Feb 28, 2025
FEATURES:
- [CCE-7745] Added internal pod and service network configuration

### v2.2.0 - Nov 26, 2024
FEATURES:
- Added new variable (sys_pool_node_labels) to support Kubernetes labels in the Default Node Pool

ENHANCEMENTS:
- Updated alerting module version to support deployment in regions other than West Europe (optional variable can be used to override default behaviour: resource_health_alert_location)
- Update desciption of variable service_mesh_revisions to provide more details about options available
- Updated pipelines to use v7 pipeline templates
- Release is tested with azurerm v4.10.0
- For configuring Insights in shared log analytics workspace in GWC region, conventions module v2.1.0 or higher must be used

BUG FIXES:
- Default http proxy configuration is corrected to allow resource deployment in all supported regions - conventions >= v2.0.4 version must be used!

### v2.1.0 - October 14, 2024
FEATURES:
- Module has been made compatible with deployment pipelines using identity federation. The change is transparent for users.
- New variable added to support cilium network policy: aks_network_data_plane
- Release has been tested with azurerm v4.1.0

BUG FIXES:
- Corrected variable descriptions

### v2.0.0 - September 10, 2024
FEATURES:
- New optional variable service_mesh_revisions
- New optional variable admin_group_object_ids
- Execute nexus2acr transfer when prometheus_enable is true (earlier it was executed only when ingress_type = nginx was set)
- Variable enable_nexus_direct is no longer supported
- ACR module version updated to meet azurerm v4.x requirements
- Minimum azurerm provider version raised to v4.1.0

### v1.7.0 - July 8, 2024
FEATURES:
- Support Open service mesh. New variable: open_service_mesh_enabled
- Support Istio service mesh. New variable: istio_service_mesh_enabled

### v1.6.1 - July 1, 2024
BUG FIXES:
- Only example bug fix: in the 11-aks-with-subnet example, route table creation had property disable_bgp_route_propagation = false which triggered plan change after each apply. It is changed to true which is required for AKS route tables.

### v1.6.0 - June 10, 2024
ENHANCEMENTS:
FEATURES:
- Updated module testing to use Terraform test and added new pipelines. This test is using Terraform 1.7.4.
- Added solution to test module in tst and prd environments and new execute pipeline
- Added custom logging feature
- Added .config file to enable new release pipeline to run
- Added new output: azurerm_kubernetes_cluster.aks_cluster.key_vault_secrets_provider.secret_identity

- examples/15-aks-auto-start-stop
  - Added support for setting the exact minutes for the logic app workflow to run.
  - Checks are being evaluated in Central Europe Standard Timezone instead of UTC.
  - Clusters without the required tags (aks-scheduled-start, aks-scheduled-stop) will be skipped.

BUG FIXES:
- AKS Start-Stop logic app workflow failed, when there were clusters in the subscription without the required tags.
- AKS Start-Stop failed, when no tags are present on a cluster

### v1.5.0 - May 01, 2024
FEATURES:
- Support Azure CNI Overlay networking. Default networking remains kubenet.
  - Following parameters must be configured to enable CNI Overlay:
    - aks_network_plugin_mode = "overlay"
    - aks_network_plugin = "azure"
- Added support_plan with valid options: KubernetesOfficial, AKSLongTermSupport
- New variable to allow configuration of os disk size: sys_pool_os_disk_size (defaults to 128 GB)

ENHANCEMENTS:
- sku_tier variable extended to support Premium as well. Defaults to Free on DEV and TST while Standard on PPR and PRD.
- Required provider version increased to 3.84 to support support_plan property
- Removed container_insights_la_workspace_id variable because the workspace id need to be defined in loganalytics.oms variable

BUG FIXES:
- Allow user node pools to be created in separate subnet (assigning network contributor on vnet level is required)
  - Role assignment aks-subnet-role-assignment will be replaced in case of module upgrade

### v1.4.4 - March 21, 2024
FEATURES:
- Default logging configuration has been removed
  - default logging of following event is no longer set (as per security decision): kube-apiserver, kube-audit, kube-controller-manager, kube-scheduler, cluster-autoscaler, kube-audit-admin, guard, cloud-controller-manager, csi-azuredisk-controller, csi-azurefile-controller, csi-snapshot-controller
  - configure logging carefully as per you needs

BUG FIXES:
- Private endpoint of ACR was recreated at every change
  - lower function is removed from ACR module call
  - user needs to use upper and lowercase characters precisely in virtual network and resource group name at the azurerm_subnet data call to avoid replacement of ACR's private endpoint
  
### v1.4.3 - February 27, 2024
FEATURES:
- Supporting customer-managed key encryption of managed disks in AKS (CCE-5764)
  - Important note: AKS module cannot be used to generate key and disk encryption set. See example 14 for an end to end use case including key and encryption set generation.
  - Introducing new variables: aks_cmk_enabled, aks_disk_encryption_set_id
    - aks_disk_encryption_set_id: ID of the disk encryption set used to encrypt node disks and persistent volumes.
    - aks_cmk_enabled: Set this variable to true if you would like to create disk encryption set and AKS cluster in one deployment step. If aks_disk_encryption_id already exists this variable can be omitted.

ENHANCEMENTS:
- Sequences are added to module tf file names
- Node pool host_encryption_enabled variable default value change to true

### v1.4.2 - February 22, 2024
FEATURES:
- Introducing variable aks_workload_identity_enabled to allow using Azure AD Workload identity. Defaults to false. (JIRA: CCE-5693)
- Introducing variable aks_oidc_issuer_enabled to allow enabling or disabling the OIDC issuer URL (automatically enabled when aks_workload_identity_enabled is true).

ENHANCEMENTS:
- Updated module versions in examples
- Removed provider maximum versions from provider.tf to allow upgrades (azurerm 4.0 will be released in few weeks)

### v1.4.1 - February 15, 2024
ENHANCEMENTS:
- Introducing variable aks_run_command_enabled to meet policy requirement. Defaults to false.

### v1.4.0 - February 1, 2024
FEATURES:
- Support user node pool creation via aks_nodepool variable (see examples/13-aks-with-nodepool for complete example)

### v1.3.0 - December 13, 2023
FEATURES:
- Integrated Container Insights logging in the module. New parameters introduced: container_insights_enable, container_insights_la_workspace_id.
  - Container Insights works with shared log analytics workspace.
  - Config map must be created along with Terraform files, see an example in /examples/12-aks-with-builtin-insights

ENHANCEMENTS:
- Ingress-Nginx versions are updated to meet security requirements: v1.9.5
- Subnet ID converted to lowercase in ACR creation which may trigger a private endpoint replacememt
- Subnet information need to be passed in subnet object 
  - Removed variables: vnet_name, vnet_rg_name, subnet_name
  - New variable: subnet_object
- External ACR need to defined as an object (new variable: external_acr, removed variables: external_acr_name, external_acr_rg)
- Removed secret blocks to store fqdn and public certificate in key vault since they can be fetched from data Source: azurerm_kubernetes_cluster

### v1.2.0 - December 4, 2023 [obsolete]
ENHANCEMENTS:
- Added ingress_timeout and prometheus_timeout parameters for helm deployments.
- Moved locals to locals.tf
- Moved data to data.tf
- Allow custom tags with aks_tags variable

BUG FIXES:
- Added acr_suffix variable in order to fix issue where acr module resource_name_suffix was statically set. This was causing issues with deployments which were run by same team in different dev subscriptions, as the acr name must be globally unique.

### v1.1.0 - October 27, 2023
FEATURES:
- Using rg module v1.1.0
- Using nexus2acr module v1.1.0
- Using alerting module v1.1.0
- Added PR template for main branch

ENHANCEMENTS:
- Execute, Semantic, Terratest and TFDocs pipelines are configured to run on the aks centralagent dev pool
- Raised minimum azurerm provider version to [3.59.0](https://github.com/hashicorp/terraform-provider-azurerm/blob/main/CHANGELOG-v3.md#3590-june-01-2023) to support certain property updates through cycling of the system node pool
- Raised maximum azurerm provider version to > 4.0.0

### v1.0.0 - October 4, 2023
- [CCE-3345] fix, missing setting
- [CCE-3897] OMS AMA log example added, tested
- [CCE-4141] Nexus2Acr lifecycle fixed - allow module upgrade, when the container images changed
- Provider changes, cleanup
- Module prepared for release v1.0.0
- Updated module versions
- Updated provider versions
### v0.8.0 - September 13, 2023
- Upgrade ACR to v0.4.2
- Upgrade Nexus2Acr to v0.2.1
- Upgrade Ingress to v1.8.1 (Chart v4.7.1)
- Upgrade Prometheus to v2.46.0 (Chart v24.0.0)
- [CCE-3824] Add Alerting
- Move k8s.io image sourcing from otphu github to ccoe-infdev-docker-release.otpnexus.hu
- [CCE-3857] auto_scaler_profile settings
- [Audit-4.1.5.2] deprecated variables deleted
- [Audit-4.2.2.6] AAD integration enforced, local accounts disabled, deprecated parameters removed
- [CCE-3345] Add zone setting to the default node pool
- Fix - helm uninstall when the install not exists
- Known issue: At the destry the module throw an error 500 on the azurerm_security_center_assessment_policy resource. The second destroy round finish successfully. Issue filed: https://github.com/hashicorp/terraform-provider-azurerm/issues/23311
### v0.7.0 - July 7, 2023
- Remove Key Vault dependency for integrated Helm deployments (Ingress, Prometheus)
- Upgrade ACR to v0.4.0
- Upgrade Nexus2Acr to v0.2.0
- Add external log configuration
### v0.6.2 - June 30, 2023
- Checkov CKV_TF_1 remediation
- [CCE-2390] Enable Azure Defender for Containers
- Fix: no-proxy issue on kubelogin download (ingress, prometheus)
### v0.6.1 - June 16, 2023
- Enable OMS Agent - Log Analytics used from conventions
- Fix: subscription handling in the ingress/prometheus deployment
- [CCE-3026] - fix ACR disable issue
### v0.6.0 - June 7, 2023
- Add external CA certificate installation option (Preview), require CustomCATrustPreview enabled in Microsoft.ContainerService namespace at subscription level. Only required for AKS use OTP Nexus directly.
- [CCE-2403] Pull policy for ACR
- [CCE-2833] Switch the proxy from IP to FQDN (Conventions module change, require conventions module v0.5.7 and above)
- Remove public_network_access_enabled = false, https://github.com/Azure/AKS/issues/3690
- Added catalog items (only for reference, no support)
### v0.6.0-B4 [deprecated]
- Add Ingress externalTrafficPolicy configuration
- Remove unnecessary secrets
- Add elasticsearch catalog item
- Add kibana catalog item
- Switch examples to helm kubelogin auth process
- [CCE-2391] Disable local admin
- Add vault.azure.net to the no_proxy list
### v0.6.0-B3 [deprecated]
- Switch to Ingress-Nginx Helm v4.6.1
- Add Prometheus install option v22.5.0
- Fix: sysctl config
- Ingress default namespace changed from default to ingress
Known issues:
- kubelogin download fail if already exists
### v0.6.0-B2 - May 9, 2023 [deprecated]
- [CCE-2404] Restrict the image pull to the connected ACR
- Fix: remove tfcoremock provider configuration
  Warrning:
  - In the blueprint you should add the following for the propper operation into your provider.tf:
        provider tfcoremock {
            use_only_state = true
        }
### v0.6.0-B1 - May the 4th be with you, 2023 [deprecated]
- [CCE-2628] Switch nginx-ingress to providerless helm
  Restrictions:
  - Only works on linux
  - The linux environment must conatin the executing service principal secret in ARM_CLIENT_SECRET variable
### v0.5.2 - April 30, 2023 [deprecated]
- [CCE-2627] Add fs.file-max parameter
- [CCE-2584] Switch to ACR v0.3.3
### v0.5.1 - April 26, 2023 [deprecated]
- CCE-1924: Storage access examples (Azure Disk / NFS Blob / NFS Azurefile)
- Update documentation (tfdocs)
### v0.5.0 - April 20, 2023 [deprecated]
- Repo restructuring (https://confluence.otpbank.hu/x/dVbwKw)
- Switch to terraform-azurerm-acr module v0.3.2
- CCE-1924: Enable Storage CSI (blob, disk, file)
- CCE-2236: Enable Azure Policy Agent (Gatekeeper)
- CCE-2381: temporary_name_for_rotation set
- CCE-2386: Default nodepool parameters
- CCE-2237: Add Key Vault CSI integration
- CCE-2240: Enforce node encryption
- CCE-2234: Enforce Standard tier in UAT/PRD
- CCE-2235: Switch off auto upgrade
- Maximum upgrade surge is set
- Switched to Key Vault based Helm provider keys
- Add external ACR capability
### v0.4.0 [deprecated]
- Switch to terraform-azurerm-acr module v0.3.0
- Externalize resource group - the resource group name can be provided as variable optionaly
- Externalize Kubernetes version
- Add user, and password id of the ACR to the output
- CCEHD-93: Switch AKS managed identity from custom role to Managed Identity Operator
- Fix: tfcoremock behaviour while the http proxy configuration switched off
- CCE-1374: Nginx Ingress controller added