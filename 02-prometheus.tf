# $$$$$$$\  $$$$$$$\   $$$$$$\  $$\      $$\ $$$$$$$$\ $$$$$$$$\ $$\   $$\ $$$$$$$$\ $$\   $$\  $$$$$$\  
# $$  __$$\ $$  __$$\ $$  __$$\ $$$\    $$$ |$$  _____|\__$$  __|$$ |  $$ |$$  _____|$$ |  $$ |$$  __$$\ 
# $$ |  $$ |$$ |  $$ |$$ /  $$ |$$$$\  $$$$ |$$ |         $$ |   $$ |  $$ |$$ |      $$ |  $$ |$$ /  \__|
# $$$$$$$  |$$$$$$$  |$$ |  $$ |$$\$$\$$ $$ |$$$$$\       $$ |   $$$$$$$$ |$$$$$\    $$ |  $$ |\$$$$$$\  
# $$  ____/ $$  __$$< $$ |  $$ |$$ \$$$  $$ |$$  __|      $$ |   $$  __$$ |$$  __|   $$ |  $$ | \____$$\ 
# $$ |      $$ |  $$ |$$ |  $$ |$$ |\$  /$$ |$$ |         $$ |   $$ |  $$ |$$ |      $$ |  $$ |$$\   $$ |
# $$ |      $$ |  $$ | $$$$$$  |$$ | \_/ $$ |$$$$$$$$\    $$ |   $$ |  $$ |$$$$$$$$\ \$$$$$$  |\$$$$$$  |
# \__|      \__|  \__| \______/ \__|     \__|\________|   \__|   \__|  \__|\________| \______/  \______/ 
resource "tfcoremock_complex_resource" "helm-prometheus" {
  count = var.prometheus_enable ? 1 : 0
  depends_on = [
    module.nexus2acr
    , azurerm_kubernetes_cluster.aksc
    , azurerm_role_assignment.aks-current-rbac-admin-role-assignment
  ]
  lifecycle {
    replace_triggered_by = [
      azurerm_kubernetes_cluster.aksc
    ]
    ignore_changes = [
      id
    ]
  }
  map = {
    "aks_name" : {
      string = azurerm_kubernetes_cluster.aksc.name
    },
    "rg" : {
      string = var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name
    },
    "sp_id" : {
      string = data.azurerm_client_config.current.client_id
    },
    "tenant_id" : {
      string = data.azurerm_client_config.current.tenant_id
    },
    "subscription_id" : {
      string = data.azurerm_client_config.current.subscription_id
    },
    "prometheus_namespace" : {
      string = var.prometheus_namespace
    }
  }
  provisioner "local-exec" {
    interpreter = ["/bin/bash", "-c"]
    command     = <<EOT
      if [[ -f "$(pwd)/bin/linux_amd64/kubelogin" ]]
      then
        export PATH=$PATH:$(pwd)/bin/linux_amd64/
      fi    
      if ! command -v kubelogin &> /dev/null
      then
        wget https://otpnexus.hu/repository/anonymous-proxy-ra-github.com/Azure/kubelogin/releases/download/v0.0.29/kubelogin-linux-amd64.zip --no-proxy
        unzip -n kubelogin-linux-amd64.zip
        export PATH=$PATH:$(pwd)/bin/linux_amd64/
      fi

      # Added retry logic to manage intermittent login issues
      if [ ! -z $ARM_CLIENT_SECRET ]
      then
        counter=0
        while [ $counter -lt 10 ]
        do                
              az login --service-principal -u ${data.azurerm_client_config.current.client_id} -p $ARM_CLIENT_SECRET --tenant ${data.azurerm_client_config.current.tenant_id}
              if [ $? -eq 0 ]
              then
                  echo 'SUCCESS: az login completed successfully.'
                  break
              fi

              echo 'az login failed. Retrying after 60 seconds.'
              sleep 60
              counter=$((counter + 1))
              if [ $counter -eq 9 ]
              then
                  echo 'ERROR: az login failed!'
              fi
        done
      fi

      # Added retry logic to manage delay in role assignment and other issues
      counter=0
      while [ $counter -lt 10 ]
      do
        az aks get-credentials --resource-group ${var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name} --name ${azurerm_kubernetes_cluster.aksc.name} --subscription ${data.azurerm_client_config.current.subscription_id} --overwrite-existing
        kubelogin convert-kubeconfig -l azurecli
        helm upgrade prometheus ${path.module}/helm/prometheus/${local.prometheus_helm_chart_version} --cleanup-on-fail -i --timeout ${var.prometheus_timeout}s --wait \
          --create-namespace \
          -n ${var.prometheus_namespace} \
          --set configmapReload.prometheus.image.repository=${local.container_registry}/${local.prometheus_cmap_reload_repo} \
          --set server.image.repository=${local.container_registry}/${local.prometheus_repo} \
          --set alertmanager.image.repository=${local.container_registry}/${local.prometheus_alertmanager_repo} \
          --set kube-state-metrics.image.registry=${local.container_registry} \
          --set kube-state-metrics.image.repository=${local.kube_sate_metrics_repo} \
          --set prometheus-node-exporter.image.registry=${local.container_registry} \
          --set prometheus-node-exporter.image.repository=${local.prometheus_node_exporter_repo} \
          --set prometheus-pushgateway.image.repository=${local.container_registry}/${local.prometheus_pushgateway_repo}
        if [ $? -eq 0 ]
        then
          echo 'SUCCESS: helm operation completed successfully.'
          break
        fi
          
        echo 'Helm operation failed. Retrying after 60 seconds.'
        sleep 60
        counter=$((counter + 1))
        if [ $counter -eq 9 ]
        then
          echo 'ERROR: Helm operation failed!'
        fi
      done        

    EOT
  }
  provisioner "local-exec" {
    when        = destroy
    interpreter = ["/bin/bash", "-c"]
    command     = <<EOT
      if [[ -f "$(pwd)/bin/linux_amd64/kubelogin" ]]
      then
        export PATH=$PATH:$(pwd)/bin/linux_amd64/
      fi    
      if ! command -v kubelogin &> /dev/null
      then
        wget https://otpnexus.hu/repository/anonymous-proxy-ra-github.com/Azure/kubelogin/releases/download/v0.0.29/kubelogin-linux-amd64.zip --no-proxy
        unzip kubelogin-linux-amd64.zip
        export PATH=$PATH:$(pwd)/bin/linux_amd64/
      fi

      if [ ! -z $ARM_CLIENT_SECRET ]
      then
        az login --service-principal -u ${self.map.sp_id.string} -p $ARM_CLIENT_SECRET --tenant ${self.map.tenant_id.string}
      fi
      az aks get-credentials --resource-group ${self.map.rg.string} --name ${self.map.aks_name.string} --subscription ${self.map.subscription_id.string} --overwrite-existing
      kubelogin convert-kubeconfig -l azurecli
      helm status prometheus -n ${self.map.prometheus_namespace.string} && helm uninstall prometheus --wait -n ${self.map.prometheus_namespace.string} || true
    EOT
  }
}