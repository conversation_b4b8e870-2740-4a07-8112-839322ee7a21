## Name of the module
Azure Kubernetes Services

shortname: aks

terraform resource: azurerm_kubernetes_cluster

## Short description of the module
This Terraform module deploys Azure Kubernetes Cluster.

## Detailed description on Confluence
[Azure Kubernetes Service](https://confluence.otpbank.hu/x/TWAoKQ)

## Terraform version compatibility
Terraform >= v1.3.6

## Necessary Terraform providers, and compatibility to provider versions
- provider registry.terraform.io/hashicorp/azurerm >= 4.1.0
- provider registry.terraform.io/hashicorp/time >= 0.9.1
- provider registry.terraform.io/hashicorp/tfcoremock >= 0.1.2
- provider registry.terraform.io/hashicorp/null >= 3.2.1

## Release notes – changes in the current and previous versions
[CHANGELOG.md](CHANGELOG.md)

## Resources generated by the module
- Azure Kubernetes Cluster
- Azure Container Registry
- Resource Group
- Resource health and metric alerts