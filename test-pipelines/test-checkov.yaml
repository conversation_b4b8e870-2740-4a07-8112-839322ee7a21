trigger: none

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • Checkov Tests

variables:
  - group: TEST

jobs:
- job: Control
  timeoutInMinutes: 600  # 10 hours maximum
  pool:
    name: DEV-AksPool-centralagent-Deploy
  steps:
    - checkout: self
      fetchDepth: 0
    - task: AzureCLI@2
      displayName: Control checkov test pipelines
      env:
        AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)
      inputs:
        azureSubscription: DEV-OTP-DD-COEINFDEV-sub-dev-01-FED
        scriptType: bash
        scriptLocation: inlineScript
        inlineScript: |
          az devops configure --defaults organization=https://dev.azure.com/ADOS-OTPHU-01
          az devops configure --defaults project=OTPHU-COE-TEMPLATESPEC

          config_file=$(Build.SourcesDirectory)/test-pipelines/config.json
          repos=$(jq -r '.repos[].name' $config_file)

          declare -A result_files
          declare -a pids

          result_dir="result"
          mkdir $result_dir
          max_parallel_jobs=10

          for repo in $repos; do
              [[ -z "$repo" ]] && continue
              echo "[INFO] Repo: $repo"
              echo "[INFO]   Pipeline: ${repo}_checkov"
          done

          function wait_for_jobs {
            sleep 30s
            job_wait_counter=0
            max_job_wait=240  # 2 hours (240 * 30s)
            while [ $(jobs -rp | wc -l) -ge $max_parallel_jobs ] && [ $job_wait_counter -lt $max_job_wait ]; do
              ((job_wait_counter++))
              sleep 30s
            done

            if [ $job_wait_counter -ge $max_job_wait ]; then
              echo "##vso[task.logissue type=warning]Job wait timeout reached. Some background jobs may be stuck. Active job PIDs: $(jobs -rp | tr '\n' ' ')"
            fi
          }

          function get_checkov_test_stats {
            local build_id=$1
            local repo=$2

            echo "[INFO] Getting test stats for build_id: $build_id, repo: $repo"

            if [[ -z "$build_id" ]] || [[ "$build_id" == "failed_to_trigger" ]]; then
              echo "[INFO] Invalid build_id, returning zeros"
              echo "0;0;0;0;0.0"
              return
            fi

            total_tests=0
            passed_tests=0
            failed_tests=0
            other_tests=0

            # Skip artifact download for now, go directly to API approach
            echo "[INFO] Using API approach to get test statistics"

              # Use test runs API with proper filtering
              echo "[INFO] Querying test runs API for build $build_id"
              test_runs_response=$(curl -s -H "Authorization: Bearer $AZURE_DEVOPS_EXT_PAT" \
                "https://dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_apis/test/runs?buildIds=$build_id&api-version=6.0" 2>/dev/null)

              if [[ -n "$test_runs_response" ]] && [[ "$test_runs_response" != *"<html>"* ]]; then
                test_runs_json=$(echo "$test_runs_response" | jq -r '.value // []' 2>/dev/null || echo "[]")

                if [[ "$test_runs_json" != "[]" ]] && [[ "$test_runs_json" != "null" ]]; then
                  # Filter test runs to only include those from this specific build
                  filtered_runs=$(echo "$test_runs_json" | jq -c "[.[] | select(.build.id == \"$build_id\")]" 2>/dev/null || echo "[]")
                  total_runs=$(echo "$test_runs_json" | jq length)
                  filtered_count=$(echo "$filtered_runs" | jq length)
                  echo "[INFO] Found $total_runs total test runs, $filtered_count for build $build_id"

                  if [[ "$filtered_runs" != "[]" ]] && [[ "$filtered_runs" != "null" ]]; then
                    echo "[INFO] Processing $filtered_count test runs for build $build_id..."
                    while IFS= read -r test_run; do
                      [[ -z "$test_run" ]] && continue

                      run_id=$(echo "$test_run" | jq -r '.id // empty' 2>/dev/null)
                      run_name=$(echo "$test_run" | jq -r '.name // "unknown"' 2>/dev/null)
                      if [[ -n "$run_id" ]] && [[ "$run_id" != "empty" ]]; then

                        # Get test run statistics directly from the run object
                        run_total=$(echo "$test_run" | jq -r '.totalTests // 0' 2>/dev/null || echo "0")
                        run_passed=$(echo "$test_run" | jq -r '.passedTests // 0' 2>/dev/null || echo "0")
                        run_failed=$(echo "$test_run" | jq -r '.unanalyzedTests // 0' 2>/dev/null || echo "0")
                        run_incomplete=$(echo "$test_run" | jq -r '.incompleteTests // 0' 2>/dev/null || echo "0")
                        run_not_executed=$(echo "$test_run" | jq -r '.notApplicableTests // 0' 2>/dev/null || echo "0")

                        # If the run object doesn't have summary stats, get detailed results
                        if [[ "$run_total" == "0" ]]; then
                          test_results_response=$(curl -s -H "Authorization: Bearer $AZURE_DEVOPS_EXT_PAT" \
                            "https://dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_apis/test/runs/$run_id/results?api-version=6.0" 2>/dev/null)

                          if [[ -n "$test_results_response" ]] && [[ "$test_results_response" != *"<html>"* ]]; then
                            run_total=$(echo "$test_results_response" | jq -r '.count // 0' 2>/dev/null || echo "0")
                            run_passed=$(echo "$test_results_response" | jq -r '[.value[] | select(.outcome == "Passed")] | length' 2>/dev/null || echo "0")
                            run_failed=$(echo "$test_results_response" | jq -r '[.value[] | select(.outcome == "Failed")] | length' 2>/dev/null || echo "0")
                            run_other=$(echo "$test_results_response" | jq -r '[.value[] | select(.outcome != "Passed" and .outcome != "Failed")] | length' 2>/dev/null || echo "0")
                          else
                            run_other=$((run_incomplete + run_not_executed))
                          fi
                        else
                          run_other=$((run_incomplete + run_not_executed))
                        fi

                        echo "[INFO] Run $run_id ($run_name): total=$run_total, passed=$run_passed, failed=$run_failed, other=$run_other"

                        total_tests=$((total_tests + run_total))
                        passed_tests=$((passed_tests + run_passed))
                        failed_tests=$((failed_tests + run_failed))
                        other_tests=$((other_tests + run_other))
                      fi
                    done < <(echo "$filtered_runs" | jq -c '.[]' 2>/dev/null || echo "")
                  else
                    echo "[INFO] No test runs found for build $build_id after filtering"
                  fi
                else
                  echo "[INFO] No test runs found in API response"
                fi
              else
                echo "[INFO] Test runs API call failed"
              fi

            echo "[INFO] Final stats for $repo: total=$total_tests, passed=$passed_tests, failed=$failed_tests, other=$other_tests"

            # Calculate pass percentage
            pass_percentage="0.0"
            if [[ $total_tests -gt 0 ]]; then
              pass_percentage=$(awk "BEGIN {printf \"%.2f\", $passed_tests * 100 / $total_tests}")
            fi

            echo "$total_tests;$passed_tests;$failed_tests;$other_tests;$pass_percentage"
          }

          function process_repo {
            local repo=$1

            wait_for_jobs

            full_output=$(az pipelines run --name "${repo}_checkov" --branch "refs/heads/main" --query "id" --output tsv 2>&1)
            if [[ $full_output =~ ^[0-9]+$ ]]; then
              build_id=$full_output
            else
              build_id=""
            fi

            key="${repo}#main#${repo}_checkov"
            result_file="$result_dir/$key.result"
            result_files[$key]=$result_file

            if [[ -z "$build_id" ]]; then
              echo "##vso[task.logissue type=warning]FAILED to trigger ${repo}_checkov pipeline for $repo. Output: $full_output"
              echo "failed_to_trigger;failed;0;0;0;0;0.0" > $result_file
              return
            fi

            echo "[INFO] Started $repo/main/${repo}_checkov: $build_id"

            status=""
            timeout_counter=0
            max_timeout=120  # 2 hours (120 * 60s)
            while [[ $status != "completed" ]] && [[ $timeout_counter -lt $max_timeout ]]; do
              sleep 60s
              ((timeout_counter++))
              status=$(az pipelines runs show --id "$build_id" --query "status" --output tsv || echo "failed")

              # Log every 30 minutes
              if [[ $((timeout_counter % 30)) -eq 0 ]]; then
                echo "[INFO] Still waiting for $repo/main/${repo}_checkov (build $build_id): $status ($timeout_counter/$max_timeout min)"
              fi

              # Break if status indicates completion or failure
              if [[ $status == "failed" ]] || [[ $status == "cancelled" ]]; then
                echo "[INFO] Pipeline $repo/main/${repo}_checkov ended with status: $status"
                break
              fi
            done

            # Check for timeout and get final result
            if [[ $timeout_counter -ge $max_timeout ]]; then
              status="timeout"
              result="timeout"
              echo "##vso[task.logissue type=warning]Pipeline $repo/main/${repo}_checkov timed out after $max_timeout minutes"
              echo "$build_id;$result;0;0;0;0;0.0" > $result_file
            else
              result=$(az pipelines runs show --id "$build_id" --query "result" --output tsv || echo "failed")
              if [[ $result != "succeeded" ]]; then
                echo "##vso[task.logissue type=warning]Pipeline $repo/main/${repo}_checkov completed with result: $result"
              else
                echo "[INFO] Pipeline $repo/main/${repo}_checkov completed successfully"
              fi

              # Get test statistics
              echo "[INFO] Collecting test statistics for $repo (build $build_id)..."
              test_stats=$(get_checkov_test_stats "$build_id" "$repo")
              echo "[INFO] Test stats for $repo: $test_stats"

              echo "$build_id;$result;$test_stats" > $result_file
            fi
            echo "Completed processing repo: $repo"
          }

          pids=()
          declare -A pid_to_repo
          for repo in $repos; do
            wait_for_jobs
            echo "Launching process for repo: $repo"
            process_repo "$repo" &
            current_pid=$!
            pids+=($current_pid)
            pid_to_repo[$current_pid]=$repo
          done

          # Wait for all background processes
          wait_counter=0
          while [ ${#pids[@]} -gt 0 ]; do
            for i in "${!pids[@]}"; do
              pid="${pids[$i]}"
              repo_name="${pid_to_repo[$pid]}"
              
              if ! kill -0 "$pid" 2>/dev/null; then
                echo "[INFO] Background process for '$repo_name' has finished"
                unset 'pids[$i]'
                unset 'pid_to_repo[$pid]'
              else
                if [[ $wait_counter -gt 0 && $((wait_counter % 30)) -eq 0 ]]; then
                  echo "[INFO] Background process for '$repo_name' is still running..."
                fi
              fi
            done
            
            pids=("${pids[@]}")
            
            if [ ${#pids[@]} -gt 0 ]; then
              sleep 60
              ((wait_counter++))
            fi
          done
          
          echo "All background processes completed"

          # Collect all test results grouped by repo
          declare -A test_results_grouped
          for result_file in $result_dir/*.result; do
            key=$(basename "$result_file" .result)
            formatted_key=$(echo "$key" | tr '#' '/')

            content=$(cat "$result_file")
            build_id=$(echo "$content" | cut -d';' -f1)
            result=$(echo "$content" | cut -d';' -f2)
            total_tests=$(echo "$content" | cut -d';' -f3)
            passed_tests=$(echo "$content" | cut -d';' -f4)
            failed_tests=$(echo "$content" | cut -d';' -f5)
            other_tests=$(echo "$content" | cut -d';' -f6)
            pass_percentage=$(echo "$content" | cut -d';' -f7)

            repo=$(echo "$formatted_key" | cut -d'/' -f1)
            tag=$(echo "$formatted_key" | cut -d'/' -f2)
            pipeline=$(echo "$formatted_key" | cut -d'/' -f3)

            # Clean values to avoid JSON parsing issues
            clean_tag=$(echo "$tag" | tr -d '\000-\037')
            clean_pipeline=$(echo "$pipeline" | tr -d '\000-\037')
            clean_result=$(echo "$result" | tr -d '\000-\037')
            clean_build_id=$(echo "$build_id" | tr -d '\000-\037')

            # Build test stats JSON object
            test_stats_json='{"totalTests":'$total_tests',"passed":'$passed_tests',"failed":'$failed_tests',"others":'$other_tests',"passPercentage":'$pass_percentage'}'

            test_results_grouped["$repo"]+='{"tag":"'$clean_tag'","pipeline":"'$clean_pipeline'","result":"'$clean_result'","buildId":"'$clean_build_id'","testStats":'$test_stats_json'},'
          done

          # Save all tests data for further processing (structured JSON)
          if [ ${#test_results_grouped[@]} -gt 0 ]; then
            json_output="{"
            first=true
            for repo in "${!test_results_grouped[@]}"; do
              if ! $first; then
                json_output+=","
              fi
              json_output+="\"$repo\":["
              json_output+="${test_results_grouped[$repo]%?}"  # Remove last comma
              json_output+="]"
              first=false
            done
            json_output+="}"
            echo "$json_output" | jq . > $(Pipeline.Workspace)/checkov_results.json
            echo "All checkov test results saved to checkov_results.json"

            # Display summary of test statistics
            echo "=== CHECKOV TEST STATISTICS SUMMARY ==="
            for repo in "${!test_results_grouped[@]}"; do
              echo "Repository: $repo"
              echo "$json_output" | jq -r ".\"$repo\"[] | \"  Pipeline: \(.pipeline) | Total: \(.testStats.totalTests) | Passed: \(.testStats.passed) | Failed: \(.testStats.failed) | Others: \(.testStats.others) | Pass Rate: \(.testStats.passPercentage)%\""
            done
            echo "========================================"
          fi
    - task: PublishPipelineArtifact@1
      displayName: Publish checkov_results.json artifact
      condition: always()
      inputs:
        targetPath: '$(Pipeline.Workspace)/checkov_results.json'
        artifact: 'checkov_results'
        publishLocation: 'pipeline'
