# Test History API Implementation - Változtatások

## Probléma leírása

A korábbi implementáció a Test Runs API-t használta (`/_apis/test/runs?buildIds=$build_id`), amely **minden test futást visszaadott**, nem csak a legutolsót. Ez azt jelentette, hogy:

- Felesleges adatok letöltése történt
- Nem volt megbízható a legutolsó futás eredményeinek kinyerése
- Teljesítmény problémák léptek fel

## Megoldás: Test History API

A Microsoft Test History API (`/_apis/testresults/results/testhistory`) pontosan erre a célra készült:
- Csak a legutolsó test eredményeket adja vissza
- Támogatja a szűrést ág és időszak szerint
- Optimalizált a teljesítményre

## Implementált változtatások

### 1. Új függvény: `get_pipeline_definition_id`

```bash
function get_pipeline_definition_id {
  local pipeline_name=$1
  
  # Azure CLI használata a pipeline definition ID megszerzésére
  az_output=$(az pipelines show --name "$pipeline_name" --query "id" --output tsv 2>&1)
  
  # Hibakezelés és validáció
  if [[ $az_exit_code -eq 0 ]] && [[ "$az_output" =~ ^[0-9]+$ ]]; then
    echo "$az_output"
  else
    echo ""
  fi
}
```

### 2. Módosított `get_checkov_test_stats` függvény

#### Kulcs változtatások:

1. **Pipeline Definition ID megszerzése**:
   ```bash
   pipeline_name="${repo}_checkov"
   definition_id=$(get_pipeline_definition_id "$pipeline_name")
   ```

2. **Test History API hívás**:
   ```bash
   # POST kérés JSON payload-dal
   json_payload='{
     "buildDefinitionId": '$definition_id',
     "trendDays": 1,
     "groupBy": "Branch", 
     "branch": "refs/heads/main"
   }'
   
   curl -X POST \
     "https://vstmr.dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_apis/testresults/results/testhistory?api-version=7.1-preview.2" \
     -d "$json_payload"
   ```

3. **Javított hibakezelés**:
   - HTTP status code ellenőrzés
   - JSON validáció
   - Részletes debug logging
   - Graceful fallback zero eredményekre

4. **Optimalizált eredmény feldolgozás**:
   ```bash
   # Csoportok feldolgozása (main branch)
   while IFS= read -r group; do
     group_results=$(echo "$group" | jq -r '.results // []')
     
     # Test eredmények feldolgozása
     while IFS= read -r test_result; do
       outcome=$(echo "$test_result" | jq -r '.outcome')
       case "$outcome" in
         "Passed") ((passed_tests++)) ;;
         "Failed") ((failed_tests++)) ;;
         *) ((other_tests++)) ;;
       esac
     done
   done
   ```

## API Endpoint változás

### Régi (Test Runs API):
```
GET https://dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_apis/test/runs?buildIds=$build_id&api-version=6.0
```

### Új (Test History API):
```
POST https://vstmr.dev.azure.com/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_apis/testresults/results/testhistory?api-version=7.1-preview.2
```

## Előnyök

1. **Pontosság**: Csak a legutolsó test eredményeket kapjuk
2. **Teljesítmény**: Kevesebb adat letöltése
3. **Megbízhatóság**: Specifikus API a test history lekérdezésére
4. **Hibakezelés**: Robusztus error handling és logging
5. **Karbantarthatóság**: Tisztább, érthetőbb kód

## Tesztelési javaslatok

1. **Manuális tesztelés**: Egy checkov pipeline futtatása és az eredmények ellenőrzése
2. **Logging ellenőrzés**: Debug üzenetek követése a pipeline logokban
3. **Edge case tesztelés**: 
   - Nem létező pipeline
   - API hiba esetén
   - Üres test eredmények esetén

## Visszaállítás terv

Ha problémák merülnének fel, a régi implementáció visszaállítható a git history-ból:
```bash
git checkout HEAD~1 -- test-pipelines/test-checkov.yaml
```
