$Region = "westeurope"
$VMSKUs = Get-AzComputeResourceSku -location $Region | Where-Object { $_.ResourceType.Contains("virtualMachines") }
foreach ($Sku in $VMSKUs)
{   
    $ephemeral_supported = $false
    $encryption_supported = $false
    foreach($capability in $Sku.Capabilities)
    {
        if($capability.Name -contains "EphemeralOSDiskSupported")
        {
            $ephemeral_supported = ($capability.Value -eq "true")
        }
        if($capability.Name -contains "EncryptionAtHostSupported")
        {
            $encryption_supported = ($capability.Value -eq "true")
        }
    }
    if($ephemeral_supported -and $encryption_supported)
    {
        Write-Host $Sku.Name
    }
}
