variable "conventions" {
  description = "(Required) terraform-conventions module"
}

variable "resource_name_suffix" {
  type        = string
  description = "(Required) Custom resource name suffix"
}

variable "aks_rg_name" {
  type        = string
  default     = null
  description = "(Optional) Name of the Azure Resource Group where the AKS created - will be automatically named if not provided"
}

variable "subnet_object" {
  description = "(Required) Subnet object where the resources will be installed to"
}

variable "use_proxy" {
  type        = bool
  default     = true
  description = "(Optional) Use proxy server for the public internet access. Default: true"
}

variable "http_proxy_override" {
  type        = string
  default     = ""
  description = "(Optional) Override the default proxy server. The default reside in the convention module. Format: http://<server>:<port>/"
}

variable "no_proxy" {
  type        = list(any)
  default     = ["otpnexus.hu", "otptesztnexus.hu", "otpbank.hu", "repsrv02lpr.kozpont.otp", "azurecr.io", "vault.azure.net"]
  description = "(Optional) List of the addresses accessed without proxy. Azure own addresses automatically included. Changing it will force the AKS to recreate."
}

variable "sys_pool_max_pods" {
  type        = number
  default     = 50
  description = "(Optional) Maximum number of pods per node. Default: 50"
}

variable "sys_pool_node_size" {
  type        = string
  default     = "Standard_D4ds_v5"
  description = "(Optional) VM Istance type and size for the system node pool."
}

variable "sys_pool_os_disk_size" {
  type        = string
  default     = null
  description = "(Optional) The size of the OS Disk in GB which should be used for each agent in the Node Pool."
}

variable "sys_pool_node_count" {
  type        = number
  default     = null
  description = "(Optional) Initial number of nodes in the system node pool. Changing after creation require the autoscaling to be switched off."
}

variable "sys_pool_enable_autoscaling" {
  type        = bool
  default     = true
  description = "(Optional) System node pool autoscaling enable. Default: enabled."
}

variable "sys_pool_node_count_min" {
  type        = number
  default     = 1
  description = "(Optional) Minimum number of nodes in the system node pool."
}

variable "sys_pool_node_count_max" {
  type        = number
  default     = 1
  description = "(Optional) Maximum number of nodes in the system node pool."
}

variable "sys_pool_zones" {
  type        = list(string)
  default     = null
  description = "(Optional) Zones used for the system node pool node placement"
}

variable "sys_pool_node_labels" {
  type = map(string)
  description = "(Optional) A map of Kubernetes labels which should be applied to nodes in the Default Node Pool."
  default = null
}

variable "ingress_type" {
  type        = string
  default     = "none"
  description = "(Optional) Type of the ingress controller preinstalled to the cluster: agic - Add-on based Azure Gateway Ingress Controller, nginx - Kubernetes Nginx Ingress Controller, none - no ingress controller installed"
  validation {
    condition     = contains(["agic", "nginx", "none"], var.ingress_type)
    error_message = "Valid values for var: ingress_type are (agic, nginx, none)."
  }
}

variable "agic_subnet_id" {
  type        = string
  default     = ""
  description = "(Conditionally required) Subnet ID for Application Gateway Ingress Controller. Required if ingress_type = agic"
}

variable "ingress_nginx_ip" {
  type        = string
  default     = "auto" // later on automatic allocation planned. Not yet implemented
  description = "(Conditionally required) IP Address of the loadbalancer for the Nginx Ingress controller. Required if ingress_type = nginx"
}

variable "ingress_nginx_preserve_source_ip" {
  type        = bool
  default     = false
  description = "(Optional) Set ingress controller.service.externalTrafficPolicy to Local"
}

variable "ingress_namespace" {
  type        = string
  default     = "ingress"
  description = "(Optional) Namespace of the ingress controller (only used by ingress-nginx)"
}

variable "ingress_timeout" {
  type        = number
  default     = 1200
  description = "(Optional) Timeout for Ingress Nginx deployment in seconds."
}

// linux profile
variable "linux_admin_usr" {
  type        = string
  description = "(Optional) Name of the linux administrator, if provided. Disbled in production."
  default     = null
}

variable "linux_admin_key" {
  type        = string
  description = "(Optional) SSH public key of the linux administrator, if provided. Disbled in production."
  default     = null
}

// Enable usage of own ACR for the cluster
variable "acr_enable" {
  type        = bool
  default     = true
  description = "(Optional) Enable Azure Container Registry. If enabled, container registry automatically created."
}

variable "external_acr" {
  default     = null
  description = "(Optional) If you want to use existing ACR you need to pass its data object to this variable."
}

variable "acr_generate_admin_token" {
  type        = bool
  default     = false
  description = "(Optional) Generate and export admin access token to key vault"
}

variable "key_vault_id" {
  type        = string
  default     = null
  description = "(Optional) Key Vault id for admin access token"
}

variable "acr_admin_token_user" {
  type        = string
  default     = "admintoken"
  description = "(Optional) Admin token name - used as username while accessing ACR"
}

variable "acr_log_analytics_workspace_id" {
  description = "(Optional) ID of target Log Analytics Workspace for ACR logging."
  type        = string
  default     = null
}

variable "acr_log_analytics_diag_logs" {
  description = "(Optional)List log types need to be sent to Log Analytics Workspace from ACR. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories"
  type        = list(string)
  default     = []
}

variable "acr_log_analytics_metrics" {
  description = "(Optional)List metrics need to be sent to Log Analytics Workspace from ACR. Set AllMetrics to send all available metric types."
  type        = list(string)
  default     = []
}

variable "aks_version" {
  type        = string
  default     = null
  description = "(Optional) Expected Kubernetes version - if not provided, the latest will be used"
}

variable "aks_automatic_upgrade_channel" {
  type        = string
  default     = "none"
  description = "(Optional) The upgrade channel for this Kubernetes Cluster. Possible values are patch, rapid, node-image and stable. Omitting this field sets this value to none."
  validation {
    condition     = contains(["patch", "rapid", "node-image", "stable", "none"], var.aks_automatic_upgrade_channel)
    error_message = "Valid values for var: aks_automatic_upgrade_channel are (patch, rapid, node-image, stable, none)."
  }
}

variable "aks_node_os_upgrade_channel" {
  type        = string
  default     = "None"
  description = "(Optional) The upgrade channel for this Kubernetes Cluster Nodes' OS Image. Possible values are Unmanaged, SecurityPatch, NodeImage and None. Defaults to NodeImage."
  validation {
    condition     = contains(["Unmanaged", "SecurityPatch", "NodeImage", "None"], var.aks_node_os_upgrade_channel)
    error_message = "Valid values for var: aks_node_os_upgrade_channel are (Unmanaged, SecurityPatch, NodeImage, None)."
  }
}

variable "aks_maintenance_window_auto_upgrade" {
  type = object({
    frequency = optional(string)
    interval = optional(number)
    duration = optional(number)
    day_of_week = optional(string)
    day_of_month = optional(string)
    week_index = optional(string)
    start_time = optional(string)
    utc_offset = optional(string)
    start_date = optional(string)
    not_allowed = optional(list(object({
      start = string
      end = string
    })), [])
  })
  default = null
  description = "(Optional) Maintenance window for auto upgrade"
}

variable "aks_maintenance_window_node_os" {
  type = object({
    frequency = optional(string)
    interval = optional(number)
    duration = optional(number)
    day_of_week = optional(string)
    day_of_month = optional(string)
    week_index = optional(string)
    start_time = optional(string)
    utc_offset = optional(string)
    start_date = optional(string)
    not_allowed = optional(list(object({
      start = string
      end = string
    })), [])
  })
  default = null
  description = "(Optional) Maintenance window for node OS upgrade"
}

variable "sys_pool_max_surge" {
  type        = string
  default     = "100%"
  description = "(Optional) Maximum surge for system node pool"
}

variable "sys_pool_drain_timeout_in_minutes" {
  type        = string
  default     = null
  description = "(Optional) Drain timeout in minutes for system node pool"
}

variable "sys_pool_node_soak_duration_in_minutes" {
  type        = string
  default     = null
  description = "(Optional) Node soak duration in minutes for system node pool"
}

variable "aks_run_command_enabled" {
  type        = bool
  default     = false
  description = "(Optional) Whether to enable run command for the cluster or not. Defaults to false."
}

variable "aks_workload_identity_enabled" {
  type        = bool
  default     = false
  description = "(Optional) Specifies whether Azure AD Workload Identity should be enabled for the Cluster. AKS supports Microsoft Entra Workload ID on version 1.22 and higher. Defaults to false."
}

variable "aks_oidc_issuer_enabled" {
  type        = bool
  default     = false
  description = "(Optional) Enable or Disable the OIDC issuer URL. After enabling OIDC issuer on the cluster, it's not supported to disable it. Defaults to false."
}

variable "aks_cmk_enabled" {
  type        = bool
  default     = false
  description = "(Optional) Set this variable to true if you would like to create disk encryption set and AKS cluster in one deployment step (see example 14). It defaults to false. If aks_disk_encryption_id already exists this variable can be omitted."
}

variable "aks_disk_encryption_set_id" {
  type        = string
  default     = null
  description = "(Optional) ID of the disk encryption set used to encrypt node disks and persistent volumes. For more details please visit https://docs.microsoft.com/en-us/azure/aks/azure-disk-customer-managed-keys. "
}

variable "storage_blob" {
  type        = bool
  default     = true
  description = "(Optional) Enable Blob CSI driver - Needed for Storage Account Blob access"
}

variable "storage_disk" {
  type        = bool
  default     = true
  description = "(Optional) Enable Disk CSI driver"
}

variable "storage_file" {
  type        = bool
  default     = true
  description = "(Optional) Enable File CSI driver"
}

variable "storage_key_vault_id" {
  type        = string
  default     = null
  description = "(Optional) Id of the key vault where the storage account user managed key published. Kubelet need permission to read the key for decryption"
}

variable "sysctl_config" {
  type = object({
    fs_aio_max_nr                      = optional(number)
    fs_file_max                        = optional(number)
    fs_inotify_max_user_watches        = optional(number)
    fs_nr_open                         = optional(number)
    kernel_threads_max                 = optional(number)
    net_core_netdev_max_backlog        = optional(number)
    net_core_optmem_max                = optional(number)
    net_core_rmem_default              = optional(number)
    net_core_rmem_max                  = optional(number)
    net_core_somaxconn                 = optional(number)
    net_core_wmem_default              = optional(number)
    net_core_wmem_max                  = optional(number)
    net_ipv4_ip_local_port_range_min   = optional(number)
    net_ipv4_ip_local_port_range_max   = optional(number)
    net_ipv4_neigh_default_gc_thresh1  = optional(number)
    net_ipv4_neigh_default_gc_thresh2  = optional(number)
    net_ipv4_neigh_default_gc_thresh3  = optional(number)
    net_ipv4_tcp_fin_timeout           = optional(number)
    net_ipv4_tcp_keepalive_intvl       = optional(number)
    net_ipv4_tcp_keepalive_probes      = optional(number)
    net_ipv4_tcp_keepalive_time        = optional(number)
    net_ipv4_tcp_max_syn_backlog       = optional(number)
    net_ipv4_tcp_max_tw_buckets        = optional(number)
    net_ipv4_tcp_tw_reuse              = optional(bool)
    net_netfilter_nf_conntrack_buckets = optional(number)
    net_netfilter_nf_conntrack_max     = optional(number)
    vm_max_map_count                   = optional(number)
    vm_swappiness                      = optional(number)
    vm_vfs_cache_pressure              = optional(number)
  })
  default = {
    fs_aio_max_nr                      = null
    fs_file_max                        = null
    fs_inotify_max_user_watches        = null
    fs_nr_open                         = null
    kernel_threads_max                 = null
    net_core_netdev_max_backlog        = null
    net_core_optmem_max                = null
    net_core_rmem_default              = null
    net_core_rmem_max                  = null
    net_core_somaxconn                 = null
    net_core_wmem_default              = null
    net_core_wmem_max                  = null
    net_ipv4_ip_local_port_range_min   = null
    net_ipv4_ip_local_port_range_max   = null
    net_ipv4_neigh_default_gc_thresh1  = null
    net_ipv4_neigh_default_gc_thresh2  = null
    net_ipv4_neigh_default_gc_thresh3  = null
    net_ipv4_tcp_fin_timeout           = null
    net_ipv4_tcp_keepalive_intvl       = null
    net_ipv4_tcp_keepalive_probes      = null
    net_ipv4_tcp_keepalive_time        = null
    net_ipv4_tcp_max_syn_backlog       = null
    net_ipv4_tcp_max_tw_buckets        = null
    net_ipv4_tcp_tw_reuse              = null
    net_netfilter_nf_conntrack_buckets = null
    net_netfilter_nf_conntrack_max     = null
    vm_max_map_count                   = null
    vm_swappiness                      = null
    vm_vfs_cache_pressure              = null
  }
  description = "(Optional) Modify sysclt parameters for the system nodepool"
}

variable "enable_secret_rotation" {
  type        = bool
  default     = true
  description = "(Optional) Secret CSI enable secret rotations"
}

variable "secret_rotation_interval" {
  type        = string
  default     = "2m"
  description = "(Optional) Secret CSI secret rotation intervall"
}

variable "sku_tier" {
  type        = string
  default     = null
  description = "(Optional) The SKU Tier that should be used for this Kubernetes Cluster. Possible values are Free, Standard (which includes the Uptime SLA) and Premium. Defaults to Free on DEV and TST, Standard on PPR and PRD."
}

variable "support_plan" {
  type = string
  validation {
    condition     = contains(["KubernetesOfficial", "AKSLongTermSupport"], var.support_plan)
    error_message = "Valid values for var: KubernetesOfficial, AKSLongTermSupport."
  }
  default     = "KubernetesOfficial"
  description = "(Optional) Specifies the support plan which should be used for this Kubernetes Cluster. Possible values are KubernetesOfficial and AKSLongTermSupport. Defaults to KubernetesOfficial."
}

variable "secret_expiration_date" {
  type        = string
  description = "(Optional) Expiration date of the Kubernetes secrets stored in the key vault. Defaults to one year (it is acceptable for dev). On UAT, PPR and PRD environments this parameter needs to be set manually as expiration date should be less than 6 months."
  default     = null
}

variable "prometheus_enable" {
  type        = bool
  default     = false
  description = "(Optional) Enable Prometheus installation"
}

variable "prometheus_namespace" {
  type        = string
  default     = "monitoring"
  description = "(Optional) Namespace of the Prometheus"
}

variable "prometheus_timeout" {
  type        = number
  default     = 1200
  description = "(Optional) Timeout for Prometheus deployment in seconds."
}

variable "admin_group_object_ids" {
  type = list(string)
  default = null
  description = "(Optional) A list of Object IDs of Azure Active Directory Groups which should have Admin Role on the Cluster."
}

variable "enable_acr_pull_policy" {
  type        = bool
  default     = true
  description = "(Optional) Enable pull policy for the provided/integrated ACR. If switched off external policy required"
}

variable "loganalytics" {
  type = object({
    defender = optional(string)
    oms      = optional(string)
    diag = optional(list(object({
      workspace_id = string
      log          = optional(list(string))
      metric       = optional(list(string))
    })))
  })
  default = {
    defender = ""
    oms      = ""
    diag = [
      {
        workspace_id = ""
        log          = []
        metric       = []
      }
    ]
  }
  description = <<EOT
  (Optional) Logging configuration.
    defender - (Optional) Specifies the ID of the Log Analytics Workspace where the audit logs collected by Microsoft Defender should be sent to.
    oms      - (Optional) The ID of the Log Analytics Workspace which the OMS Agent should send data to. Defaults to the shared log analytics workspaces.
    diag block supports the following attributes:
      workspace_id - (Optional) ID of target Log Analytics Workspace
      log    - (Optional) List log types need to be sent to Log Analytics Workspace.
      metric - (Optional) List metrics need to be sent to Log Analytics Workspace."
  EOT
}

variable "builtin_metric_monitoring" {
  description = "(Optional) Set to false if default alerting rules are not required. Defaults to true"
  type        = bool
  default     = true
}

variable "resource_health_monitoring" {
  description = "(Optional) Set to false if resource health alert rule is not required. Defaults to true."
  type        = bool
  default     = true
}

variable "resource_health_alert_location" {
  type        = string
  description = "(Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings."
  default     = null  
}

variable "alert_acr_StorageUsed_threshold" {
  description = "(Optional) Threshold for StorageUsed alert rule."
  type        = number
  default     = 483183820800 // 450GB
}

variable "alert_node_cpu_usage_percentage_threshold" {
  description = "(Optional) Threshold for Node CPU Usage Percentage alert rule."
  type        = number
  default     = 90
}

variable "alert_node_disk_usage_percentage_threshold" {
  description = "(Optional) Threshold for Node CPU Usage Percentage alert rule."
  type        = number
  default     = 90
}

variable "alert_cluster_autoscaler_unschedulable_pods_count_threshold" {
  description = "(Optional) Threshold for Cluster Autoscaler Unschedulable Pods Count alert rule."
  type        = number
  default     = 0
}

variable "alert_kube_pod_status_phase_threshold" {
  description = "(Optional) Threshold for Pods Failed/Unknown state alert rule."
  type        = number
  default     = 0
}

variable "acr_suffix" {
  description = "(Optional) ACR suffix need to be defined in case of resource name conflict"
  type        = string
  default     = ""
}

variable "auto_scaler_profile" {
  type = object({
    balance_similar_node_groups      = optional(bool)
    expander                         = optional(string) // Possible values: least-waste, priority, most-pods, random
    max_graceful_termination_sec     = optional(number)
    max_node_provisioning_time       = optional(string)
    max_unready_nodes                = optional(number)
    max_unready_percentage           = optional(number)
    new_pod_scale_up_delay           = optional(string)
    scale_down_delay_after_add       = optional(string)
    scale_down_delay_after_delete    = optional(string)
    scale_down_delay_after_failure   = optional(string)
    scan_interval                    = optional(string)
    scale_down_unneeded              = optional(string)
    scale_down_unready               = optional(string)
    scale_down_utilization_threshold = optional(number)
    empty_bulk_delete_max            = optional(number)
    skip_nodes_with_local_storage    = optional(bool)
    skip_nodes_with_system_pods      = optional(bool)
  })
  default = {
    balance_similar_node_groups      = false
    expander                         = "random"
    max_graceful_termination_sec     = 600
    max_node_provisioning_time       = "15m"
    max_unready_nodes                = 3
    max_unready_percentage           = 45
    new_pod_scale_up_delay           = "10s"
    scale_down_delay_after_add       = "10m"
    scale_down_delay_after_delete    = "10s"
    scale_down_delay_after_failure   = "3m"
    scan_interval                    = "10s"
    scale_down_unneeded              = "10m"
    scale_down_unready               = "20m"
    scale_down_utilization_threshold = 0.5
    empty_bulk_delete_max            = 10
    skip_nodes_with_local_storage    = true
    skip_nodes_with_system_pods      = true
  }
  description = "(Optional) Auto scaler profile configuration."
}

variable "aks_tags" {
  description = "(Optional) Tags to applied to AKS resources"
  type        = map(string)
  default     = null
}

variable "container_insights_enable" {
  type        = bool
  default     = false
  description = "(Optional) Enable container insights logging. Defaults to false."
}

variable "container_insights_configmap_filename" {
  description = "(Optional) Filename of configmap file which must be created along with Terraform files. It is mandatory in case container_insights_enable is true."
  type        = string
  default     = null
}

variable "aks_network_plugin" {
  description = "(Optional) Network plugin to use for networking. Currently supported values are azure, kubenet and none. Changing this forces a new resource to be created."
  type        = string
  default     = "azure"

  validation {
    condition = (
      var.aks_network_plugin == "kubenet" ||
      var.aks_network_plugin == "azure" ||
      var.aks_network_plugin == "none"
    )
    error_message = "Possible values are kubenet, azure and none."
  }
}

variable "aks_network_data_plane" {
  description = "(Optional) Specifies the data plane used for building the Kubernetes network. Possible values are azure and cilium. Defaults to azure. Disabling this forces a new resource to be created."
  default     = null
  type        = string
}

variable "aks_network_plugin_mode" {
  description = "(Optional) Specifies the network plugin mode used for building the Kubernetes network. Possible value is overlay."
  type        = string
  default     = "overlay"

  validation {
    condition = (
      var.aks_network_plugin_mode == "overlay" ||
      var.aks_network_plugin_mode == null
    )
    error_message = "Possible value is overlay."
  }
}

variable "pod_cidr" {
  description = "(Optional) The CIDR of the pod network."
  type        = string
  default     = null
}

variable "service_cidr" {
  description = "(Optional) The CIDR of the service network."
  type        = string
  default     = null
}

variable "aks_outbound_type" {
  description = "(Optional) The outbound (egress) routing method which should be used for this Kubernetes Cluster. Possible values are loadBalancer, userDefinedRouting, managedNATGateway and userAssignedNATGateway. Defaults to userDefinedRouting. Changing this forces a new resource to be created."
  type        = string
  default     = "userDefinedRouting"

  validation {
    condition = (
      var.aks_outbound_type == "loadBalancer" ||
      var.aks_outbound_type == "userDefinedRouting" ||
      var.aks_outbound_type == "managedNATGateway" ||
      var.aks_outbound_type == "userAssignedNATGateway"
    )
    error_message = "Possible values are loadBalancer, userDefinedRouting, managedNATGateway and userAssignedNATGateway."
  }
}

variable "aks_network_policy" {
  description = "(Optional) Sets up network policy to be used. Network policy allows us to control the traffic flow between pods. Currently supported values are calico, azure and cilium. Changing this forces a new resource to be created. For more details please visit https://docs.microsoft.com/en-us/azure/aks/use-network-policies."
  type        = string
  default     = "azure"

  validation {
    condition = (
      var.aks_network_policy == "calico" ||
      var.aks_network_policy == "azure" ||
      var.aks_network_policy == "cilium" ||
      var.aks_network_policy == null
    )
    error_message = "Possible values are calico, azure and cilium."
  }
}

variable "aks_network_dns_service_ip" {
  description = "(Optional) IP address within the Kubernetes service address range that will be used by cluster service discovery (kube-dns). Changing this forces a new resource to be created."
  type        = string
  default     = null

  validation {
    condition     = (
    can(regex("^((10\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})|(172\\.(1[6-9]|2\\d|3[01])\\.\\d{1,3}\\.\\d{1,3})|(192\\.168\\.\\d{1,3}\\.\\d{1,3}))$", var.aks_network_dns_service_ip)) ||
    var.aks_network_dns_service_ip == null
    )
    error_message = "The provided IP address is not a valid RFC1918 IPv4 address."
  }

}

variable "open_service_mesh_enabled" {
  description = "(Optional) Is Open Service Mesh enabled? For more details, please visit Open Service Mesh for AKS. Defaults to false."
  type        = bool
  default     = false
}

variable "istio_service_mesh_enabled" {
  description = "(Optional) Is Istio Service Mesh enabled. Defaults to false."
  type        = bool
  default     = false
}

variable "service_mesh_internal_ingress_gateway_enabled" {
  description = "(Optional) Is Istio Internal Ingress Gateway enabled? Defaults to false."
  type        = bool
  default     = false
}

variable "service_mesh_revisions" {
  type        = list(string)
  default     = null
  description = <<EOT
    (Optional) Required when istio_service_mesh_enabled is true. 
    Specify 1 or 2 Istio control plane revisions for managing minor upgrades using the canary upgrade process. For example, create the resource with revisions set to [asm-1-22], or leave it empty (the revisions will only be known after apply). To start the canary upgrade, change revisions to [asm-1-22, asm-1-23]. To roll back the canary upgrade, revert to [asm-1-22]. To confirm the upgrade, change to [asm-1-23].
    Supported versions can be checked with following command: az aks mesh get-revisions --location westeurope -o table
    Further documentation versioning: https://learn.microsoft.com/en-us/azure/aks/istio-support-policy
  EOT
}

variable "service_mesh_external_ingress_gateway_enabled" {
  description = "(Optional) Is Istio External Ingress Gateway enabled? Defaults to false."
  type        = bool
  default     = false
}

variable "only_critical_addons_enabled" {
  description = "(Optional) Enabling this option will taint default node pool with CriticalAddonsOnly=true:NoSchedule taint. temporary_name_for_rotation must be specified when changing this property."
  type        = bool
  default     = false
}

variable "temporary_name_for_rotation" {
  description = "(Optional) Specifies the name of the temporary node pool used to cycle the default node pool for VM resizing."
  type        = string
  default     = "systemtmp"
}

variable "insights_custom_data_flows" {
  description = "(Optional) Custom data flows for container insights."
  type        = list(object({
    destinations = optional(list(string))
    streams = list(string)
    built_in_transform = optional(string)
    output_stream = optional(string)
    transform_kql = optional(string)
  }))
  default     = []
}