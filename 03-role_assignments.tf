resource "azurerm_role_assignment" "aks-subnet-role-assignment" {
  principal_id         = azurerm_user_assigned_identity.umid-aks.principal_id
  role_definition_name = "Network Contributor"
  scope                = local.vnet_id // Need to assign the role on vnet level since there might be multiple subnets used by node pools
}

resource "azurerm_role_assignment" "aks-route-table-role-assignment" {
  principal_id         = azurerm_user_assigned_identity.umid-aks.principal_id
  role_definition_name = "Network Contributor"
  scope                = var.subnet_object.route_table_id
}

resource "azurerm_role_assignment" "kubelet-role-assignment" {
  principal_id         = azurerm_user_assigned_identity.umid-aks.principal_id
  role_definition_name = "Managed Identity Operator"
  scope                = azurerm_user_assigned_identity.umid-kubelet.id
}

resource "azurerm_role_assignment" "acr-kubelet-role-assignment" {
  count                = var.external_acr == null ? 0 : 1
  principal_id         = azurerm_user_assigned_identity.umid-kubelet.principal_id
  role_definition_name = "AcrPull"
  scope                = var.external_acr.id
}

resource "azurerm_role_assignment" "aks-current-rbac-admin-role-assignment" {
  principal_id         = data.azurerm_client_config.current.object_id
  role_definition_name = "Azure Kubernetes Service RBAC Cluster Admin"
  scope                = azurerm_kubernetes_cluster.aksc.id
}

### Disk Encryption Set Reader ###
resource "azurerm_role_assignment" "aks_cluster_role_des_reader" {
  count                = local.cmk_enabled ? 1 : 0
  scope                = var.aks_disk_encryption_set_id
  role_definition_name = "Reader"
  principal_id         = azurerm_kubernetes_cluster.aksc.kubelet_identity[0].object_id
}

resource "azurerm_role_assignment" "aks_cluster_role_des_cluster_reader" {
  count                = local.cmk_enabled ? 1 : 0
  scope                = var.aks_disk_encryption_set_id
  role_definition_name = "Reader"
  principal_id         = azurerm_user_assigned_identity.umid-aks.principal_id
}

