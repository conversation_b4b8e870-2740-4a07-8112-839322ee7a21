# $$\   $$\ $$\      $$\ $$$$$$\ $$$$$$$\  
# $$ |  $$ |$$$\    $$$ |\_$$  _|$$  __$$\ 
# $$ |  $$ |$$$$\  $$$$ |  $$ |  $$ |  $$ |
# $$ |  $$ |$$\$$\$$ $$ |  $$ |  $$ |  $$ |
# $$ |  $$ |$$ \$$$  $$ |  $$ |  $$ |  $$ |
# $$ |  $$ |$$ |\$  /$$ |  $$ |  $$ |  $$ |
# \$$$$$$  |$$ | \_/ $$ |$$$$$$\ $$$$$$$  |
#  \______/ \__|     \__|\______|\_______/ 
resource "azurerm_user_assigned_identity" "umid-kubelet" {
  location            = var.conventions.region
  name                = local.name_umid_kubelet
  resource_group_name = var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name
  tags                = local.tags
}

resource "azurerm_user_assigned_identity" "umid-aks" {
  location            = var.conventions.region
  name                = local.name_umid_aks
  resource_group_name = var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name
  tags                = local.tags
}

# $$$$$$$\   $$$$$$\  $$$$$$$\  $$$$$$$\  
# $$  __$$\ $$  __$$\ $$  __$$\ $$  __$$\ 
# $$ |  $$ |$$ /  \__|$$ |  $$ |$$ |  $$ |
# $$$$$$$  |$$ |$$$$\ $$$$$$$  |$$$$$$$  |
# $$  __$$< $$ |\_$$ |$$  __$$< $$  ____/ 
# $$ |  $$ |$$ |  $$ |$$ |  $$ |$$ |      
# $$ |  $$ |\$$$$$$  |$$ |  $$ |$$ |      
# \__|  \__| \______/ \__|  \__|\__|      
module "rgrp" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  count                = var.aks_rg_name == null ? 1 : 0
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = var.conventions
  resource_name_suffix = var.resource_name_suffix
}

#  $$$$$$\   $$$$$$\  $$$$$$$\  
# $$  __$$\ $$  __$$\ $$  __$$\ 
# $$ /  $$ |$$ /  \__|$$ |  $$ |
# $$$$$$$$ |$$ |      $$$$$$$  |
# $$  __$$ |$$ |      $$  __$$< 
# $$ |  $$ |$$ |  $$\ $$ |  $$ |
# $$ |  $$ |\$$$$$$  |$$ |  $$ |
# \__|  \__| \______/ \__|  \__|
module "acre" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  #checkov:skip=CKV_AZURE_166: The image scan/quarantine is not used until the remediation developed
  #checkov:skip=CKV_AZURE_233: Currently zone redundancy is not a requirement for ACR and it is not supported from module
  depends_on = [
    azurerm_user_assigned_identity.umid-kubelet
  ]
  source                      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-acr?ref=v1.5.0"
  conventions                 = var.conventions
  resource_name_suffix        = "aks${var.acr_suffix}"
  resource_group_name         = var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name
  subnet_id                   = var.subnet_object.id
  acr_umid_principal_ids      = [azurerm_user_assigned_identity.umid-kubelet.principal_id]
  acr_enable                  = var.acr_enable && var.external_acr == null
  generate_admin_token        = var.acr_generate_admin_token
  key_vault_id                = var.key_vault_id
  admin_token_user            = var.acr_admin_token_user
  log_analytics_workspace_id  = var.acr_log_analytics_workspace_id
  log_analytics_diag_logs     = var.acr_log_analytics_diag_logs
  log_analytics_metrics       = var.acr_log_analytics_metrics
  builtin_metric_monitoring   = var.builtin_metric_monitoring
  resource_health_monitoring  = var.resource_health_monitoring
  resource_health_alert_location = var.resource_health_alert_location
  alert_StorageUsed_threshold = var.alert_acr_StorageUsed_threshold
}


resource "tfcoremock_complex_resource" "sysctl_config" {
  object = var.sysctl_config
}

#  $$$$$$\  $$\   $$\  $$$$$$\  
# $$  __$$\ $$ | $$  |$$  __$$\ 
# $$ /  $$ |$$ |$$  / $$ /  \__|
# $$$$$$$$ |$$$$$  /  \$$$$$$\  
# $$  __$$ |$$  $$<    \____$$\ 
# $$ |  $$ |$$ |\$$\  $$\   $$ |
# $$ |  $$ |$$ | \$$\ \$$$$$$  |
# \__|  \__|\__|  \__| \______/ 
resource "azurerm_kubernetes_cluster" "aksc" {
  depends_on = [
    azurerm_role_assignment.aks-subnet-role-assignment,
    azurerm_role_assignment.aks-route-table-role-assignment,
    azurerm_role_assignment.kubelet-role-assignment
  ]
  lifecycle {
    replace_triggered_by = [
      tfcoremock_complex_resource.sysctl_config
    ]
    ignore_changes = [
      default_node_pool[0].linux_os_config[0].sysctl_config
    ]
  }
  // Checkov
  #checkov:skip=CKV_AZURE_171:No automatic upgrades to keep the production systems intact
  #checkov:skip=CKV2_AZURE_29:Project decision - using kubenet instead of Azure CNI
  #checkov:skip=CKV_AZURE_7:Network Profile require Azure CNI - for kubenet, not needed
  #checkov:skip=CKV_AZURE_117:The disk_encryption_set_id is part of the CMK decision. On hold until it decided - Using node based encryption, may override this
  #checkov:skip=CKV_AZURE_232: There is no support for second node pool yet hence we need to skip this
  #checkov:skip=CKV_AZURE_170:Paid SKU only enforced for production systems
  name                = local.name_aksc
  location            = var.conventions.region
  resource_group_name = var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name
  node_resource_group = "${var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name}-nodes"
  run_command_enabled = var.aks_run_command_enabled

  // Version and upgrade
  sku_tier            = var.sku_tier == null ? ( local.is_production ? "Standard" : "Free" ) : var.sku_tier
  support_plan        = var.support_plan
  kubernetes_version  = var.aks_version

  automatic_upgrade_channel = var.aks_automatic_upgrade_channel == "none" ? null : var.aks_automatic_upgrade_channel
  node_os_upgrade_channel = var.aks_node_os_upgrade_channel

  dynamic "maintenance_window_auto_upgrade" {
    for_each = var.aks_automatic_upgrade_channel == "none" || var.aks_maintenance_window_auto_upgrade == null ? [] : [1]
    content {
      duration = var.aks_maintenance_window_auto_upgrade.duration
      frequency = var.aks_maintenance_window_auto_upgrade.frequency
      interval = var.aks_maintenance_window_auto_upgrade.interval
      day_of_week = var.aks_maintenance_window_auto_upgrade.day_of_week
      day_of_month = var.aks_maintenance_window_auto_upgrade.day_of_month
      week_index = var.aks_maintenance_window_auto_upgrade.week_index
      start_time = var.aks_maintenance_window_auto_upgrade.start_time
      utc_offset = var.aks_maintenance_window_auto_upgrade.utc_offset
      start_date = var.aks_maintenance_window_auto_upgrade.start_date
      dynamic "not_allowed" {
        for_each = var.aks_maintenance_window_auto_upgrade.not_allowed
        content {
          start = not_allowed.start
          end = not_allowed.end
        }
      }
    }
  }

  dynamic "maintenance_window_node_os" {
    for_each = var.aks_node_os_upgrade_channel == "None" || var.aks_maintenance_window_node_os == null ? [] : [1]
    content {
      duration = var.aks_maintenance_window_node_os.duration
      frequency = var.aks_maintenance_window_node_os.frequency
      interval = var.aks_maintenance_window_node_os.interval
      day_of_week = var.aks_maintenance_window_node_os.day_of_week
      day_of_month = var.aks_maintenance_window_node_os.day_of_month
      week_index = var.aks_maintenance_window_node_os.week_index
      start_time = var.aks_maintenance_window_node_os.start_time
      utc_offset = var.aks_maintenance_window_node_os.utc_offset
      start_date = var.aks_maintenance_window_node_os.start_date
      dynamic "not_allowed" {
        for_each = var.aks_maintenance_window_node_os.not_allowed
        content {
          start = not_allowed.start
          end = not_allowed.end
        }
      }
    }
  }

  // Optional
  workload_identity_enabled = var.aks_workload_identity_enabled
  oidc_issuer_enabled       = var.aks_workload_identity_enabled ? true : var.aks_oidc_issuer_enabled //To enable Azure AD Workload Identity oidc_issuer_enabled must be set to true.
  disk_encryption_set_id = var.aks_disk_encryption_set_id

  network_profile {
    network_plugin      = var.aks_network_plugin
    network_plugin_mode = var.aks_network_plugin == "azure" ? var.aks_network_plugin_mode : null
    network_mode        = var.aks_network_plugin_mode == "azure" ? "transparent" : null
    outbound_type       = var.aks_outbound_type
    network_policy      = var.aks_network_policy
    dns_service_ip      = var.aks_network_dns_service_ip
    network_data_plane  = var.aks_network_data_plane
    pod_cidr            = var.pod_cidr
    service_cidr        = var.service_cidr
  }

  dns_prefix                          = local.name_aksc
  private_cluster_enabled             = true
  private_cluster_public_fqdn_enabled = false
  private_dns_zone_id                 = "System"
  open_service_mesh_enabled           = var.open_service_mesh_enabled

  dynamic "service_mesh_profile" {
    for_each = var.istio_service_mesh_enabled == true ? [1] : []
    content {
      mode = "Istio" //The mode of the service mesh. Only possible value is Istio.
      revisions                        = var.service_mesh_revisions
      internal_ingress_gateway_enabled = var.service_mesh_internal_ingress_gateway_enabled
      external_ingress_gateway_enabled = var.service_mesh_external_ingress_gateway_enabled
    }
  }

  dynamic "http_proxy_config" {
    for_each = var.use_proxy ? [1] : []
    content {
      http_proxy  = local.http_proxy
      https_proxy = local.http_proxy
      no_proxy    = var.no_proxy // add no_proxy for the private endpoint DNS, like ACR !!!
    }
  }

  storage_profile {
    blob_driver_enabled = var.storage_blob
    disk_driver_enabled = var.storage_disk
    file_driver_enabled = var.storage_file
  }

  default_node_pool {
    #checkov:skip=CKV_AZURE_226:Bug in the checkov - https://github.com/bridgecrewio/checkov/issues/5611
    #checkov:skip=CKV_AZURE_227:Bug in the checkov - https://github.com/bridgecrewio/checkov/issues/5611
    name                        = "system"
    orchestrator_version        = var.aks_version
    os_disk_size_gb             = var.sys_pool_os_disk_size
    node_count                  = var.sys_pool_node_count
    vm_size                     = var.sys_pool_node_size
    os_disk_type                = "Ephemeral"
    vnet_subnet_id              = var.subnet_object.id
    auto_scaling_enabled        = var.sys_pool_enable_autoscaling
    host_encryption_enabled     = true
    min_count                   = var.sys_pool_node_count_min
    max_count                   = var.sys_pool_node_count_max
    temporary_name_for_rotation = var.temporary_name_for_rotation
    only_critical_addons_enabled  = var.only_critical_addons_enabled
    //custom_ca_trust_enabled     = var.enable_nexus_direct
    zones                       = var.sys_pool_zones
    node_labels                 = var.sys_pool_node_labels

    // max pods
    max_pods = var.sys_pool_max_pods

    upgrade_settings {
      max_surge = var.sys_pool_max_surge
      drain_timeout_in_minutes = var.sys_pool_drain_timeout_in_minutes
      node_soak_duration_in_minutes = var.sys_pool_node_soak_duration_in_minutes
    }

    linux_os_config {
      sysctl_config {
        fs_aio_max_nr                      = var.sysctl_config.fs_aio_max_nr
        fs_file_max                        = var.sysctl_config.fs_file_max
        fs_inotify_max_user_watches        = var.sysctl_config.fs_inotify_max_user_watches
        fs_nr_open                         = var.sysctl_config.fs_nr_open
        kernel_threads_max                 = var.sysctl_config.kernel_threads_max
        net_core_netdev_max_backlog        = var.sysctl_config.net_core_netdev_max_backlog
        net_core_optmem_max                = var.sysctl_config.net_core_optmem_max
        net_core_rmem_default              = var.sysctl_config.net_core_rmem_default
        net_core_rmem_max                  = var.sysctl_config.net_core_rmem_max
        net_core_somaxconn                 = var.sysctl_config.net_core_somaxconn
        net_core_wmem_default              = var.sysctl_config.net_core_wmem_default
        net_core_wmem_max                  = var.sysctl_config.net_core_wmem_max
        net_ipv4_ip_local_port_range_min   = var.sysctl_config.net_ipv4_ip_local_port_range_min
        net_ipv4_ip_local_port_range_max   = var.sysctl_config.net_ipv4_ip_local_port_range_max
        net_ipv4_neigh_default_gc_thresh1  = var.sysctl_config.net_ipv4_neigh_default_gc_thresh1
        net_ipv4_neigh_default_gc_thresh2  = var.sysctl_config.net_ipv4_neigh_default_gc_thresh2
        net_ipv4_neigh_default_gc_thresh3  = var.sysctl_config.net_ipv4_neigh_default_gc_thresh3
        net_ipv4_tcp_fin_timeout           = var.sysctl_config.net_ipv4_tcp_fin_timeout
        net_ipv4_tcp_keepalive_intvl       = var.sysctl_config.net_ipv4_tcp_keepalive_intvl
        net_ipv4_tcp_keepalive_probes      = var.sysctl_config.net_ipv4_tcp_keepalive_probes
        net_ipv4_tcp_keepalive_time        = var.sysctl_config.net_ipv4_tcp_keepalive_time
        net_ipv4_tcp_max_syn_backlog       = var.sysctl_config.net_ipv4_tcp_max_syn_backlog
        net_ipv4_tcp_max_tw_buckets        = var.sysctl_config.net_ipv4_tcp_max_tw_buckets
        net_ipv4_tcp_tw_reuse              = var.sysctl_config.net_ipv4_tcp_tw_reuse
        net_netfilter_nf_conntrack_buckets = var.sysctl_config.net_netfilter_nf_conntrack_buckets
        net_netfilter_nf_conntrack_max     = var.sysctl_config.net_netfilter_nf_conntrack_max
        vm_max_map_count                   = var.sysctl_config.vm_max_map_count
        vm_swappiness                      = var.sysctl_config.vm_swappiness
        vm_vfs_cache_pressure              = var.sysctl_config.vm_vfs_cache_pressure
      }
    }
  }

  dynamic "linux_profile" {
    for_each = var.linux_admin_usr != null && var.linux_admin_key != null ? [1] : []
    content {
      admin_username = var.linux_admin_usr
      ssh_key {
        key_data = var.linux_admin_key
      }
    }
  }

  // Control Plane Identity
  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.umid-aks.id]
  }

  // Kubelet Identity
  kubelet_identity {
    client_id                 = azurerm_user_assigned_identity.umid-kubelet.client_id
    object_id                 = azurerm_user_assigned_identity.umid-kubelet.principal_id
    user_assigned_identity_id = azurerm_user_assigned_identity.umid-kubelet.id
  }

  dynamic "oms_agent" {
    for_each = var.loganalytics.oms == null && var.container_insights_enable == false ? [] : [1]
    content {
      log_analytics_workspace_id      = var.loganalytics.oms == "" || var.loganalytics.oms == null ? var.conventions.log_analytics_workspace_id : var.loganalytics.oms
      msi_auth_for_monitoring_enabled = true
    }
  }

  azure_policy_enabled = true

  key_vault_secrets_provider {
    secret_rotation_enabled  = var.enable_secret_rotation
    secret_rotation_interval = var.secret_rotation_interval
  }

  dynamic "microsoft_defender" {
    for_each = var.loganalytics.defender == null ? [] : [1]
    content {
      log_analytics_workspace_id = var.loganalytics.defender == "" ? var.conventions.log_analytics_workspace_id : var.loganalytics.defender
    }
  }

  // Ingress Controller

  // AGIC
  dynamic "ingress_application_gateway" {
    for_each = var.ingress_type == "agic" ? [1] : []
    content {
      subnet_id = var.agic_subnet_id
    }
  }

  local_account_disabled = true


  azure_active_directory_role_based_access_control {
    tenant_id              = data.azurerm_client_config.current.tenant_id
    admin_group_object_ids = var.admin_group_object_ids
    azure_rbac_enabled     = true
  }

  auto_scaler_profile {
    balance_similar_node_groups      = var.auto_scaler_profile.balance_similar_node_groups
    expander                         = var.auto_scaler_profile.expander
    max_graceful_termination_sec     = var.auto_scaler_profile.max_graceful_termination_sec
    max_node_provisioning_time       = var.auto_scaler_profile.max_node_provisioning_time
    max_unready_nodes                = var.auto_scaler_profile.max_unready_nodes
    max_unready_percentage           = var.auto_scaler_profile.max_unready_percentage
    new_pod_scale_up_delay           = var.auto_scaler_profile.new_pod_scale_up_delay
    scale_down_delay_after_add       = var.auto_scaler_profile.scale_down_delay_after_add
    scale_down_delay_after_delete    = var.auto_scaler_profile.scale_down_delay_after_delete
    scale_down_delay_after_failure   = var.auto_scaler_profile.scale_down_delay_after_failure
    scan_interval                    = var.auto_scaler_profile.scan_interval
    scale_down_unneeded              = var.auto_scaler_profile.scale_down_unneeded
    scale_down_unready               = var.auto_scaler_profile.scale_down_unready
    scale_down_utilization_threshold = var.auto_scaler_profile.scale_down_utilization_threshold
    empty_bulk_delete_max            = var.auto_scaler_profile.empty_bulk_delete_max
    skip_nodes_with_local_storage    = var.auto_scaler_profile.skip_nodes_with_local_storage
    skip_nodes_with_system_pods      = var.auto_scaler_profile.skip_nodes_with_system_pods
  }

  tags = local.tags
}

#  $$$$$$\  $$$$$$$$\  $$$$$$\         $$$$$$\  $$$$$$$$\ $$\   $$\ $$$$$$$$\ $$$$$$$$\ $$$$$$$\  
# $$  __$$\ $$  _____|$$  __$$\       $$  __$$\ $$  _____|$$$\  $$ |\__$$  __|$$  _____|$$  __$$\ 
# $$ /  \__|$$ |      $$ /  \__|      $$ /  \__|$$ |      $$$$\ $$ |   $$ |   $$ |      $$ |  $$ |
# \$$$$$$\  $$$$$\    $$ |            $$ |      $$$$$\    $$ $$\$$ |   $$ |   $$$$$\    $$$$$$$  |
#  \____$$\ $$  __|   $$ |            $$ |      $$  __|   $$ \$$$$ |   $$ |   $$  __|   $$  __$$< 
# $$\   $$ |$$ |      $$ |  $$\       $$ |  $$\ $$ |      $$ |\$$$ |   $$ |   $$ |      $$ |  $$ |
# \$$$$$$  |$$$$$$$$\ \$$$$$$  |      \$$$$$$  |$$$$$$$$\ $$ | \$$ |   $$ |   $$$$$$$$\ $$ |  $$ |
#  \______/ \________| \______/        \______/ \________|\__|  \__|   \__|   \________|\__|  \__|
resource "azurerm_security_center_assessment_policy" "aksc_asc_policy" {
  depends_on = [
    azurerm_kubernetes_cluster.aksc
  ]
  display_name = local.name_aksc
  severity     = "Medium"
  description  = "AKS Policy"
}

resource "azurerm_security_center_assessment" "aksc_asc_assessment" {
  depends_on = [
    azurerm_kubernetes_cluster.aksc,
    azurerm_security_center_assessment_policy.aksc_asc_policy
  ]
  assessment_policy_id = azurerm_security_center_assessment_policy.aksc_asc_policy.id
  target_resource_id   = azurerm_kubernetes_cluster.aksc.id

  status {
    code = "Healthy"
  }
}

# $$\   $$\ $$$$$$$$\ $$\   $$\ $$\   $$\  $$$$$$\   $$$$$$\   $$$$$$\   $$$$$$\  $$$$$$$\  
# $$$\  $$ |$$  _____|$$ |  $$ |$$ |  $$ |$$  __$$\ $$  __$$\ $$  __$$\ $$  __$$\ $$  __$$\ 
# $$$$\ $$ |$$ |      \$$\ $$  |$$ |  $$ |$$ /  \__|\__/  $$ |$$ /  $$ |$$ /  \__|$$ |  $$ |
# $$ $$\$$ |$$$$$\     \$$$$  / $$ |  $$ |\$$$$$$\   $$$$$$  |$$$$$$$$ |$$ |      $$$$$$$  |
# $$ \$$$$ |$$  __|    $$  $$<  $$ |  $$ | \____$$\ $$  ____/ $$  __$$ |$$ |      $$  __$$< 
# $$ |\$$$ |$$ |      $$  /\$$\ $$ |  $$ |$$\   $$ |$$ |      $$ |  $$ |$$ |  $$\ $$ |  $$ |
# $$ | \$$ |$$$$$$$$\ $$ /  $$ |\$$$$$$  |\$$$$$$  |$$$$$$$$\ $$ |  $$ |\$$$$$$  |$$ |  $$ |
# \__|  \__|\________|\__|  \__| \______/  \______/ \________|\__|  \__| \______/ \__|  \__|
resource "time_sleep" "wait-private-dns" {
  count           = ( var.ingress_type == "nginx" || var.prometheus_enable ) && var.external_acr == null ? 1 : 0
  depends_on      = [module.acre]
  create_duration = "500s"
}

module "nexus2acr" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash 
  count = var.ingress_type == "nginx" || var.prometheus_enable ? 1 : 0
  depends_on = [
    time_sleep.wait-private-dns,
    module.acre
  ]
  source        = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-tools-nexus2acr?ref=v3.0.0"
  acr_name      = var.external_acr == null ? module.acre.acre.name : var.external_acr.name
  source_images = local.container_images
  
}
