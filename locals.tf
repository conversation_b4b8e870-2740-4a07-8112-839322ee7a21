locals {
  http_proxy        = var.http_proxy_override == "" ? var.conventions.conventions.http_proxy : var.http_proxy_override
  nc_hyphen         = "${var.conventions.short_region}-${var.conventions.environment}-${var.resource_name_suffix}"
  nc_no_hyphen      = replace(local.nc_hyphen, "-", "")
  name_aksc         = "aksc-${local.nc_hyphen}"
  name_umid_aks     = "umid-${local.nc_hyphen}-aks"
  name_umid_kubelet = "umid-${local.nc_hyphen}-kubelet"
  resource_name     = "${var.conventions.short_region}-${var.conventions.environment}-${var.resource_name_suffix}"
  streams           = ["Microsoft-ContainerInsights-Group-Default"]
  cmk_enabled       = var.aks_cmk_enabled ? true : (var.aks_disk_encryption_set_id != null ? true : false)
  vnet_id           = join("/",slice(split("/",var.subnet_object.id),0,9)) 
  validate_critical_addons_usage = (var.only_critical_addons_enabled && var.temporary_name_for_rotation == "") ? "temporary_name_for_rotation must be set when only_critical_addons_enabled is true" : ""  

  // Ingress-Nginx
  container_registry            = "${var.external_acr == null ? (var.acr_enable ? module.acre.acre.name : "") : var.external_acr.name}.azurecr.io"
  registry_docker_io            = "anonymous-proxy-do-registry-1-docker-io.otpnexus.hu"
  registry_k8s_io               = "anonymous-proxy-do-ccoe-infdev-docker-release-otpnexus-hu.otpnexus.hu"
  registry_quay_io              = "anonymous-proxy-do-quay-io.otpnexus.hu"
  ingress_images_repo_ingress   = "ingress-nginx/controller"
  ingress_images_tag_controller = "v1.9.5"
  ingress_images_repo_certgen   = "ingress-nginx/kube-webhook-certgen"
  ingress_images_tag_certgen    = "v20231011-8b53cabe0"
  ingress_images_repo_backend   = "defaultbackend-amd64"
  ingress_images_tag_backend    = "1.5"
  ingress_helm_chart_version    = "v4.9.0"
  kube_sate_metrics_repo        = "kube-state-metrics/kube-state-metrics"
  kube_sate_metrics_tag         = "v2.9.2"
  prometheus_helm_chart_version = "v24.0.0"
  prometheus_cmap_reload_repo   = "prometheus-operator/prometheus-config-reloader"
  prometheus_cmap_reload_tag    = "v0.67.0"
  prometheus_repo               = "prometheus/prometheus"
  prometheus_tag                = "v2.46.0"
  prometheus_alertmanager_repo  = "prometheus/alertmanager"
  prometheus_alertmanager_tag   = "v0.26.0"
  prometheus_node_exporter_repo = "prometheus/node-exporter"
  prometheus_node_exporter_tag  = "v1.6.0"
  prometheus_pushgateway_repo   = "prom/pushgateway"
  prometheus_pushgateway_tag    = "v1.6.0"
  container_images = [
    "${local.registry_k8s_io}/${local.ingress_images_repo_ingress}:${local.ingress_images_tag_controller}",
    "${local.registry_k8s_io}/${local.ingress_images_repo_certgen}:${local.ingress_images_tag_certgen}",
    "${local.registry_k8s_io}/${local.ingress_images_repo_backend}:${local.ingress_images_tag_backend}",
    "${local.registry_quay_io}/${local.prometheus_cmap_reload_repo}:${local.prometheus_cmap_reload_tag}",
    "${local.registry_quay_io}/${local.prometheus_repo}:${local.prometheus_tag}",
    "${local.registry_quay_io}/${local.prometheus_alertmanager_repo}:${local.prometheus_alertmanager_tag}",
    "${local.registry_k8s_io}/${local.kube_sate_metrics_repo}:${local.kube_sate_metrics_tag}",
    "${local.registry_quay_io}/${local.prometheus_node_exporter_repo}:${local.prometheus_node_exporter_tag}",
    "${local.registry_docker_io}/${local.prometheus_pushgateway_repo}:${local.prometheus_pushgateway_tag}"
  ]
  is_production = var.conventions.environment == "ppr" || var.conventions.environment == "prd"

  shared_loganalytics = var.conventions.log_analytics_workspace_id

  tags = merge(
    var.conventions.tags,
    var.aks_tags,
    {
      creation_mode         = "terraform",
      terraform-azurerm-aks = "v4.0.2",
    }
  )

}