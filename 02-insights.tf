resource "azurerm_monitor_data_collection_rule" "dcr" {
  count                       = var.container_insights_enable ? 1 : 0
  name                        = "dcru-${local.resource_name}"
  resource_group_name         = var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name
  location                    = var.conventions.region
  data_collection_endpoint_id = var.conventions.dce_linux_id

  destinations {
    log_analytics {
      workspace_resource_id = var.loganalytics.oms == null || var.loganalytics.oms == "" ? var.conventions.log_analytics_workspace_id : var.loganalytics.oms
      name                  = "ciworkspace"
    }
  }

  data_flow {
    streams      = local.streams
    destinations = ["ciworkspace"]
  }
  
  dynamic "data_flow" {
    for_each = var.insights_custom_data_flows
    content {
      destinations       = data_flow.value.destinations != null ? data_flow.value.destinations : ["ciworkspace"]
      streams            = data_flow.value.streams
      built_in_transform = data_flow.value.built_in_transform
      output_stream      = data_flow.value.output_stream
      transform_kql      = data_flow.value.transform_kql
    }
  }

  data_sources {
    extension {
      streams        = local.streams
      extension_name = "ContainerInsights"
      extension_json = jsonencode({
        "dataCollectionSettings" : {
          "interval" : "1m",
          "namespaceFilteringMode" : "Off",
          "enableContainerLogV2" : true
        }
      })
      name = "ContainerInsightsExtension"
    }
  }

  description = "DCR for Azure Monitor Container Insights"
  tags        = var.conventions.tags
}

resource "azurerm_monitor_data_collection_rule_association" "dcra" {
  count                   = var.container_insights_enable ? 1 : 0
  name                    = "ContainerInsightsExtension"
  target_resource_id      = azurerm_kubernetes_cluster.aksc.id
  data_collection_rule_id = azurerm_monitor_data_collection_rule.dcr[0].id
  description             = "Association of container insights data collection rule. Deleting this association will break the data collection for this AKS Cluster."
}

resource "azurerm_monitor_data_collection_rule_association" "dcra-endpoint" {
  count                       = var.container_insights_enable ? 1 : 0
  name                        = "configurationAccessEndpoint"
  target_resource_id          = azurerm_kubernetes_cluster.aksc.id
  data_collection_endpoint_id = var.conventions.dce_linux_id
  description                 = "AKS endpoint connection"
}

resource "tfcoremock_complex_resource" "ama-config" {
  count = var.container_insights_enable ? 1 : 0
  depends_on = [
    module.nexus2acr
    , azurerm_kubernetes_cluster.aksc
    , azurerm_role_assignment.aks-current-rbac-admin-role-assignment
  ]
  lifecycle {
    replace_triggered_by = [
      azurerm_kubernetes_cluster.aksc
    ]
    ignore_changes = [
      id
    ]
  }
  map = {
    "aks_name" : {
      string = azurerm_kubernetes_cluster.aksc.name
    },
    "rg" : {
      string = var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name
    },
    "sp_id" : {
      string = data.azurerm_client_config.current.client_id
    },
    "tenant_id" : {
      string = data.azurerm_client_config.current.tenant_id
    },
    "subscription_id" : {
      string = data.azurerm_client_config.current.subscription_id
    },
    "ingress_namespace" : {
      string = var.ingress_namespace
    }
  }
  provisioner "local-exec" {
    interpreter = ["/bin/bash", "-c"]
    command     = <<EOT
        if [[ -f "$(pwd)/bin/linux_amd64/kubelogin" ]]
        then
          export PATH=$PATH:$(pwd)/bin/linux_amd64/
        fi
        if ! command -v kubelogin &> /dev/null
        then
            wget https://otpnexus.hu/repository/anonymous-proxy-ra-github.com/Azure/kubelogin/releases/download/v0.0.29/kubelogin-linux-amd64.zip --no-proxy
            unzip -n kubelogin-linux-amd64.zip
            export PATH=$PATH:$(pwd)/bin/linux_amd64/
        fi

        # Added retry logic to manage intermittent login issues
        counter=0

        if [ ! -z $ARM_CLIENT_SECRET ]
        then
          while [ $counter -lt 10 ]
          do
              az login --service-principal -u ${data.azurerm_client_config.current.client_id} -p $ARM_CLIENT_SECRET --tenant ${data.azurerm_client_config.current.tenant_id}
              if [ $? -eq 0 ]
              then
                  echo 'SUCCESS: az login completed successfully.'
                  break
              fi           
              echo 'az login failed. Retrying after 60 seconds.'
              sleep 60
              counter=$((counter + 1))
              if [ $counter -eq 9 ]
              then
                  echo 'ERROR: az login failed!'
              fi
          done

        fi

        # Added retry logic to manage delay in role assignment
        counter=0
        while [ $counter -lt 10 ]
        do
          az aks get-credentials --resource-group ${var.aks_rg_name == null ? module.rgrp[0].rgrp.name : var.aks_rg_name} --name ${azurerm_kubernetes_cluster.aksc.name} --subscription ${data.azurerm_client_config.current.subscription_id} --overwrite-existing
          if [ $? -eq 0 ]
          then
            echo 'SUCCESS: az aks get operation completed successfully.'
            break
          fi
          
          echo 'WARNING: az aks operation failed. Retrying after 60 seconds.'
          sleep 60
          counter=$((counter + 1))
          if [ $counter -eq 9 ]
          then
            echo 'ERROR: az aks get operation failed!'
          fi
        done
        kubelogin convert-kubeconfig -l azurecli
        kubectl apply -f ${var.container_insights_configmap_filename}
        kubectl -n kube-system get pods
        kubectl -n kube-system delete pods -l component=ama-logs-agent
        kubectl -n kube-system delete pods -l rsName=ama-logs-rs
    EOT
  }
}